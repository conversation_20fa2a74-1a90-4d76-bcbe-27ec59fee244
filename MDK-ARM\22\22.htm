<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [22\22.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image 22\22.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6220000: Last Updated: Wed Aug 27 11:53:44 2025
<BR><P>
<H3>Maximum Stack Usage =        484 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; TestBoard_RunAllTests &rArr; test_display_functions &rArr; Display_UpdateStatus &rArr; Display_SetComponent &rArr; Display_SendCommand &rArr; IOUART_SendByte &rArr; IOUART_PutChar
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1c]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1c]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1c]">ADC_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[4]">BusFault_Handler</a> from stm32f2xx_it.o(.text.BusFault_Handler) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[1e]">CAN1_RX0_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[1f]">CAN1_RX1_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[20]">CAN1_SCE_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[1d]">CAN1_TX_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[48]">CAN2_RX0_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[49]">CAN2_RX1_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[4a]">CAN2_SCE_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[47]">CAN2_TX_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[15]">DMA1_Stream0_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[16]">DMA1_Stream1_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[17]">DMA1_Stream2_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[18]">DMA1_Stream3_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[19]">DMA1_Stream4_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[1a]">DMA1_Stream5_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[1b]">DMA1_Stream6_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[39]">DMA1_Stream7_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[42]">DMA2_Stream0_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[43]">DMA2_Stream1_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[44]">DMA2_Stream2_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[45]">DMA2_Stream3_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[46]">DMA2_Stream4_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[4c]">DMA2_Stream5_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[4d]">DMA2_Stream6_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[4e]">DMA2_Stream7_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[7]">DebugMon_Handler</a> from stm32f2xx_it.o(.text.DebugMon_Handler) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[10]">EXTI0_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[32]">EXTI15_10_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[11]">EXTI1_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[12]">EXTI2_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[13]">EXTI3_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[14]">EXTI4_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[21]">EXTI9_5_IRQHandler</a> from stm32f2xx_it.o(.text.EXTI9_5_IRQHandler) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[e]">FLASH_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[3a]">FSMC_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[56]">HASH_RNG_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from stm32f2xx_it.o(.text.HardFault_Handler) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[2a]">I2C1_ER_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[29]">I2C1_EV_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[2c]">I2C2_ER_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[2b]">I2C2_EV_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[51]">I2C3_ER_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[50]">I2C3_EV_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[3]">MemManage_Handler</a> from stm32f2xx_it.o(.text.MemManage_Handler) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from stm32f2xx_it.o(.text.NMI_Handler) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[4b]">OTG_FS_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[34]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[53]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[52]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[55]">OTG_HS_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[54]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[b]">PVD_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[8]">PendSV_Handler</a> from stm32f2xx_it.o(.text.PendSV_Handler) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[f]">RCC_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[33]">RTC_Alarm_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[d]">RTC_WKUP_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[3b]">SDIO_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[2d]">SPI1_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[2e]">SPI2_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[3d]">SPI3_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[6]">SVC_Handler</a> from stm32f2xx_it.o(.text.SVC_Handler) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[9]">SysTick_Handler</a> from stm32f2xx_it.o(.text.SysTick_Handler) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[58]">SystemInit</a> from system_stm32f2xx.o(.text.SystemInit) referenced from startup_stm32f205xx.o(.text)
 <LI><a href="#[c]">TAMP_STAMP_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[22]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[25]">TIM1_CC_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[24]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[23]">TIM1_UP_TIM10_IRQHandler</a> from stm32f2xx_it.o(.text.TIM1_UP_TIM10_IRQHandler) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[26]">TIM2_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[27]">TIM3_IRQHandler</a> from stm32f2xx_it.o(.text.TIM3_IRQHandler) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[28]">TIM4_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[3c]">TIM5_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[40]">TIM6_DAC_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[41]">TIM7_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[35]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[38]">TIM8_CC_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[37]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[36]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[3e]">UART4_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[3f]">UART5_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[2f]">USART1_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[30]">USART2_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[31]">USART3_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[4f]">USART6_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[5]">UsageFault_Handler</a> from stm32f2xx_it.o(.text.UsageFault_Handler) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[a]">WWDG_IRQHandler</a> from startup_stm32f205xx.o(.text) referenced from startup_stm32f205xx.o(RESET)
 <LI><a href="#[59]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f205xx.o(.text)
 <LI><a href="#[5b]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0snprintf)
 <LI><a href="#[5a]">fputc</a> from fputc.o(i.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[57]">main</a> from main.o(.text.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[59]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(.text)
</UL>
<P><STRONG><a name="[d2]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[5c]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[6d]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[d3]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[d4]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[d5]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[d6]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[d7]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>FSMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f205xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[ac]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_BuildPacket
</UL>

<P><STRONG><a name="[d8]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[d9]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[62]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[da]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[db]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[61]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[6f]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_Init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[dc]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[63]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[c8]"></a>memcmp</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_packet_validation
</UL>

<P><STRONG><a name="[78]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_ShowBoardStatus
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_board_info
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_UpdateStatus
</UL>

<P><STRONG><a name="[dd]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[d1]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[60]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[de]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[5f]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[df]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[e0]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 48 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[64]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[68]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[69]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[6a]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[6b]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[6c]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[ce]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[5d]"></a>__scatterload</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[e1]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[65]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[e2]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, semi.o(.text), UNUSED)

<P><STRONG><a name="[e3]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[e4]"></a>__I$use$semihosting$fputc</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, iusesemip.o(.text), UNUSED)

<P><STRONG><a name="[67]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[66]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f2xx_it.o(.text.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f2xx_it.o(.text.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>Display_Init</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, test_board_control.o(.text.Display_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = Display_Init &rArr; Display_ShowMessage &rArr; Display_SetComponent &rArr; Display_SendCommand &rArr; IOUART_SendByte &rArr; IOUART_PutChar
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_SendCommand
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_ShowMessage
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[71]"></a>Display_SendCommand</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, test_board_control.o(.text.Display_SendCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = Display_SendCommand &rArr; IOUART_SendByte &rArr; IOUART_PutChar
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IOUART_SendString
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IOUART_SendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_SetComponent
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Init
</UL>

<P><STRONG><a name="[75]"></a>Display_SetComponent</STRONG> (Thumb, 72 bytes, Stack size 160 bytes, test_board_control.o(.text.Display_SetComponent))
<BR><BR>[Stack]<UL><LI>Max Depth = 212<LI>Call Chain = Display_SetComponent &rArr; Display_SendCommand &rArr; IOUART_SendByte &rArr; IOUART_PutChar
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_SendCommand
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_UpdateStatus
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_ShowMessage
</UL>

<P><STRONG><a name="[77]"></a>Display_ShowBoardStatus</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, test_board_control.o(.text.Display_ShowBoardStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Display_ShowBoardStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_display_functions
</UL>

<P><STRONG><a name="[72]"></a>Display_ShowMessage</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, test_board_control.o(.text.Display_ShowMessage))
<BR><BR>[Stack]<UL><LI>Max Depth = 228<LI>Call Chain = Display_ShowMessage &rArr; Display_SetComponent &rArr; Display_SendCommand &rArr; IOUART_SendByte &rArr; IOUART_PutChar
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_SetComponent
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_display_functions
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_RunAllTests
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Init
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[79]"></a>Display_UpdateStatus</STRONG> (Thumb, 274 bytes, Stack size 112 bytes, test_board_control.o(.text.Display_UpdateStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 324<LI>Call Chain = Display_UpdateStatus &rArr; Display_SetComponent &rArr; Display_SendCommand &rArr; IOUART_SendByte &rArr; IOUART_PutChar
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_SetComponent
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_GetTypeName
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_display_functions
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[21]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, stm32f2xx_it.o(.text.EXTI9_5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = EXTI9_5_IRQHandler &rArr; HAL_GPIO_EXTI_IRQHandler &rArr; HAL_GPIO_EXTI_Callback
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[a5]"></a>Error_Handler</STRONG> (Thumb, 14 bytes, Stack size 4 bytes, main.o(.text.Error_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[70]"></a>HAL_Delay</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, stm32f2xx_hal.o(.text.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_display_functions
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_board_control_commands
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_uart_communication
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_PowerOffAll
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_PowerOnAll
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Init
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7d]"></a>HAL_GPIO_EXTI_Callback</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, main.o(.text.HAL_GPIO_EXTI_Callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_GPIO_EXTI_Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>

<P><STRONG><a name="[7b]"></a>HAL_GPIO_EXTI_IRQHandler</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, stm32f2xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_GPIO_EXTI_IRQHandler &rArr; HAL_GPIO_EXTI_Callback
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI9_5_IRQHandler
</UL>

<P><STRONG><a name="[a0]"></a>HAL_GPIO_Init</STRONG> (Thumb, 970 bytes, Stack size 56 bytes, stm32f2xx_hal_gpio.o(.text.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IOUART_Init
</UL>

<P><STRONG><a name="[a1]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, stm32f2xx_hal_gpio.o(.text.HAL_GPIO_WritePin))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IOUART_Init
</UL>

<P><STRONG><a name="[7c]"></a>HAL_GetTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f2xx_hal.o(.text.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_board_info
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_ProcessHeartbeat
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_UpdateStatus
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IOUART_SendByte
</UL>

<P><STRONG><a name="[a9]"></a>HAL_IncTick</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f2xx_hal.o(.text.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[7e]"></a>HAL_Init</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, stm32f2xx_hal.o(.text.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[80]"></a>HAL_InitTick</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, stm32f2xx_hal.o(.text.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[81]"></a>HAL_MspInit</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, stm32f2xx_hal_msp.o(.text.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[84]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, stm32f2xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_NVIC_EnableIRQ &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
</UL>

<P><STRONG><a name="[83]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 50 bytes, Stack size 32 bytes, stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_EncodePriority
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_GetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
</UL>

<P><STRONG><a name="[7f]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_NVIC_SetPriorityGrouping &rArr; __NVIC_SetPriorityGrouping
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[8a]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 600 bytes, Stack size 24 bytes, stm32f2xx_hal_rcc.o(.text.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[8b]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 230 bytes, Stack size 32 bytes, stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[8c]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 1578 bytes, Stack size 32 bytes, stm32f2xx_hal_rcc.o(.text.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[82]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, stm32f2xx_hal_cortex.o(.text.HAL_SYSTICK_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_SYSTICK_Config &rArr; SysTick_Config &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[9b]"></a>HAL_TIMEx_BreakCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIMEx_BreakCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[9d]"></a>HAL_TIMEx_CommutCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIMEx_CommutCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[a6]"></a>HAL_TIMEx_MasterConfigSynchronization</STRONG> (Thumb, 290 bytes, Stack size 20 bytes, stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_TIMEx_MasterConfigSynchronization
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[8e]"></a>HAL_TIM_Base_Init</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[8f]"></a>HAL_TIM_Base_MspInit</STRONG> (Thumb, 204 bytes, Stack size 32 bytes, stm32f2xx_hal_msp.o(.text.HAL_TIM_Base_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[a2]"></a>HAL_TIM_Base_Start_IT</STRONG> (Thumb, 246 bytes, Stack size 12 bytes, stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_TIM_Base_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IOUART_Init
</UL>

<P><STRONG><a name="[91]"></a>HAL_TIM_ConfigClockSource</STRONG> (Thumb, 388 bytes, Stack size 32 bytes, stm32f2xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = HAL_TIM_ConfigClockSource &rArr; TIM_ETR_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETR_SetConfig
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI2_ConfigInputStage
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITRx_SetConfig
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI1_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[97]"></a>HAL_TIM_IC_CaptureCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIM_IC_CaptureCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[96]"></a>HAL_TIM_IRQHandler</STRONG> (Thumb, 538 bytes, Stack size 24 bytes, stm32f2xx_hal_tim.o(.text.HAL_TIM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = HAL_TIM_IRQHandler &rArr; HAL_TIM_PeriodElapsedCallback &rArr; iouart_TXD
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_CommutCallback
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_TriggerCallback
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_BreakCallback
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_OC_DelayElapsedCallback
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_CaptureCallback
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_PulseFinishedCallback
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_IRQHandler
<LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_UP_TIM10_IRQHandler
</UL>

<P><STRONG><a name="[98]"></a>HAL_TIM_OC_DelayElapsedCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIM_OC_DelayElapsedCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[99]"></a>HAL_TIM_PWM_PulseFinishedCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIM_PWM_PulseFinishedCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[9a]"></a>HAL_TIM_PeriodElapsedCallback</STRONG> (Thumb, 500 bytes, Stack size 40 bytes, main.o(.text.HAL_TIM_PeriodElapsedCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = HAL_TIM_PeriodElapsedCallback &rArr; iouart_TXD
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iouart_TXD
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[9c]"></a>HAL_TIM_TriggerCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32f2xx_hal_tim.o(.text.HAL_TIM_TriggerCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIM_TriggerCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f2xx_it.o(.text.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[9f]"></a>IOUART_Init</STRONG> (Thumb, 322 bytes, Stack size 64 bytes, main.o(.text.IOUART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = IOUART_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start_IT
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a3]"></a>IOUART_PutChar</STRONG> (Thumb, 128 bytes, Stack size 4 bytes, main.o(.text.IOUART_PutChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = IOUART_PutChar
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_SendPacket
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IOUART_SendString
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IOUART_SendByte
</UL>

<P><STRONG><a name="[74]"></a>IOUART_SendByte</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, main.o(.text.IOUART_SendByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = IOUART_SendByte &rArr; IOUART_PutChar
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IOUART_PutChar
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_SendCommand
</UL>

<P><STRONG><a name="[73]"></a>IOUART_SendString</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, main.o(.text.IOUART_SendString))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = IOUART_SendString &rArr; IOUART_PutChar
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IOUART_PutChar
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_uart_communication
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_SendCommand
</UL>

<P><STRONG><a name="[3]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f2xx_it.o(.text.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f2xx_it.o(.text.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f2xx_it.o(.text.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f2xx_it.o(.text.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f2xx_it.o(.text.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[aa]"></a>SystemClock_Config</STRONG> (Thumb, 118 bytes, Stack size 80 bytes, main.o(.text.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[58]"></a>SystemInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, system_stm32f2xx.o(.text.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(.text)
</UL>
<P><STRONG><a name="[23]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f2xx_it.o(.text.TIM1_UP_TIM10_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = TIM1_UP_TIM10_IRQHandler &rArr; HAL_TIM_IRQHandler &rArr; HAL_TIM_PeriodElapsedCallback &rArr; iouart_TXD
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f2xx_it.o(.text.TIM3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = TIM3_IRQHandler &rArr; HAL_TIM_IRQHandler &rArr; HAL_TIM_PeriodElapsedCallback &rArr; iouart_TXD
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[90]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 432 bytes, Stack size 12 bytes, stm32f2xx_hal_tim.o(.text.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[92]"></a>TIM_ETR_SetConfig</STRONG> (Thumb, 52 bytes, Stack size 20 bytes, stm32f2xx_hal_tim.o(.text.TIM_ETR_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_ETR_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[ab]"></a>TestBoard_BuildPacket</STRONG> (Thumb, 170 bytes, Stack size 32 bytes, test_board_control.o(.text.TestBoard_BuildPacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = TestBoard_BuildPacket &rArr; sum_array
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_array
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_error_handling
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_packet_validation
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_StopTest
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_Reset
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_PowerOff
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_SetCurrent
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_StartTest
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_SetVoltage
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_PowerOn
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_ProcessHeartbeat
</UL>

<P><STRONG><a name="[ae]"></a>TestBoard_EmergencyStop</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, test_board_control.o(.text.TestBoard_EmergencyStop))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = TestBoard_EmergencyStop &rArr; TestBoard_StopTest &rArr; TestBoard_BuildPacket &rArr; sum_array
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_StopTest
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_board_info
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_error_handling
</UL>

<P><STRONG><a name="[c5]"></a>TestBoard_GetInfo</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, test_board_control.o(.text.TestBoard_GetInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TestBoard_GetInfo
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_board_control_commands
</UL>

<P><STRONG><a name="[c7]"></a>TestBoard_GetStatus</STRONG> (Thumb, 60 bytes, Stack size 4 bytes, test_board_control.o(.text.TestBoard_GetStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = TestBoard_GetStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_error_handling
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_board_control_commands
</UL>

<P><STRONG><a name="[c9]"></a>TestBoard_GetStatusName</STRONG> (Thumb, 108 bytes, Stack size 12 bytes, test_board_control.o(.text.TestBoard_GetStatusName))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TestBoard_GetStatusName
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_board_info
</UL>

<P><STRONG><a name="[7a]"></a>TestBoard_GetTypeName</STRONG> (Thumb, 146 bytes, Stack size 12 bytes, test_board_control.o(.text.TestBoard_GetTypeName))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TestBoard_GetTypeName
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_UpdateStatus
</UL>

<P><STRONG><a name="[b1]"></a>TestBoard_Init</STRONG> (Thumb, 200 bytes, Stack size 24 bytes, test_board_control.o(.text.TestBoard_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TestBoard_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b2]"></a>TestBoard_PowerOff</STRONG> (Thumb, 114 bytes, Stack size 88 bytes, test_board_control.o(.text.TestBoard_PowerOff))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = TestBoard_PowerOff &rArr; TestBoard_BuildPacket &rArr; sum_array
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_SendPacket
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_BuildPacket
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_board_info
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_board_control_commands
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_PowerOffAll
</UL>

<P><STRONG><a name="[b4]"></a>TestBoard_PowerOffAll</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, test_board_control.o(.text.TestBoard_PowerOffAll))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = TestBoard_PowerOffAll &rArr; TestBoard_PowerOff &rArr; TestBoard_BuildPacket &rArr; sum_array
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_PowerOff
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_board_control_commands
</UL>

<P><STRONG><a name="[b5]"></a>TestBoard_PowerOn</STRONG> (Thumb, 114 bytes, Stack size 88 bytes, test_board_control.o(.text.TestBoard_PowerOn))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = TestBoard_PowerOn &rArr; TestBoard_BuildPacket &rArr; sum_array
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_SendPacket
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_BuildPacket
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_board_info
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_error_handling
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_board_control_commands
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_PowerOnAll
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b6]"></a>TestBoard_PowerOnAll</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, test_board_control.o(.text.TestBoard_PowerOnAll))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = TestBoard_PowerOnAll &rArr; TestBoard_PowerOn &rArr; TestBoard_BuildPacket &rArr; sum_array
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_PowerOn
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_board_control_commands
</UL>

<P><STRONG><a name="[b7]"></a>TestBoard_ProcessHeartbeat</STRONG> (Thumb, 150 bytes, Stack size 88 bytes, test_board_control.o(.text.TestBoard_ProcessHeartbeat))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = TestBoard_ProcessHeartbeat &rArr; TestBoard_BuildPacket &rArr; sum_array
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_SendPacket
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_BuildPacket
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b8]"></a>TestBoard_Reset</STRONG> (Thumb, 114 bytes, Stack size 88 bytes, test_board_control.o(.text.TestBoard_Reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = TestBoard_Reset &rArr; TestBoard_BuildPacket &rArr; sum_array
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_SendPacket
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_BuildPacket
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_board_info
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_board_control_commands
</UL>

<P><STRONG><a name="[b9]"></a>TestBoard_RunAllTests</STRONG> (Thumb, 120 bytes, Stack size 88 bytes, test_board_test.o(.text.TestBoard_RunAllTests))
<BR><BR>[Stack]<UL><LI>Max Depth = 452<LI>Call Chain = TestBoard_RunAllTests &rArr; test_display_functions &rArr; Display_UpdateStatus &rArr; Display_SetComponent &rArr; Display_SendCommand &rArr; IOUART_SendByte &rArr; IOUART_PutChar
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_test_summary
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_error_handling
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_display_functions
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_board_control_commands
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_packet_validation
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_uart_communication
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_ShowMessage
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b3]"></a>TestBoard_SendPacket</STRONG> (Thumb, 128 bytes, Stack size 32 bytes, test_board_control.o(.text.TestBoard_SendPacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = TestBoard_SendPacket &rArr; IOUART_PutChar
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IOUART_PutChar
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_StopTest
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_Reset
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_PowerOff
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_SetCurrent
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_StartTest
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_SetVoltage
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_PowerOn
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_ProcessHeartbeat
</UL>

<P><STRONG><a name="[c0]"></a>TestBoard_SetCurrent</STRONG> (Thumb, 154 bytes, Stack size 96 bytes, test_board_control.o(.text.TestBoard_SetCurrent))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = TestBoard_SetCurrent &rArr; TestBoard_BuildPacket &rArr; sum_array
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_SendPacket
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_BuildPacket
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_board_control_commands
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c1]"></a>TestBoard_SetVoltage</STRONG> (Thumb, 154 bytes, Stack size 96 bytes, test_board_control.o(.text.TestBoard_SetVoltage))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = TestBoard_SetVoltage &rArr; TestBoard_BuildPacket &rArr; sum_array
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_SendPacket
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_BuildPacket
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_board_control_commands
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c2]"></a>TestBoard_StartTest</STRONG> (Thumb, 140 bytes, Stack size 88 bytes, test_board_control.o(.text.TestBoard_StartTest))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = TestBoard_StartTest &rArr; TestBoard_BuildPacket &rArr; sum_array
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_SendPacket
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_BuildPacket
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_board_info
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_board_control_commands
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[af]"></a>TestBoard_StopTest</STRONG> (Thumb, 114 bytes, Stack size 88 bytes, test_board_control.o(.text.TestBoard_StopTest))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = TestBoard_StopTest &rArr; TestBoard_BuildPacket &rArr; sum_array
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_SendPacket
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_BuildPacket
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_board_info
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_board_control_commands
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_EmergencyStop
</UL>

<P><STRONG><a name="[c3]"></a>TestBoard_UpdateStatus</STRONG> (Thumb, 302 bytes, Stack size 32 bytes, test_board_control.o(.text.TestBoard_UpdateStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = TestBoard_UpdateStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_display_functions
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c4]"></a>TestBoard_ValidatePacket</STRONG> (Thumb, 158 bytes, Stack size 24 bytes, test_board_control.o(.text.TestBoard_ValidatePacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = TestBoard_ValidatePacket &rArr; sum_array
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_array
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_packet_validation
</UL>

<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f2xx_it.o(.text.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f205xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>main</STRONG> (Thumb, 302 bytes, Stack size 32 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 484<LI>Call Chain = main &rArr; TestBoard_RunAllTests &rArr; test_display_functions &rArr; Display_UpdateStatus &rArr; Display_SetComponent &rArr; Display_SendCommand &rArr; IOUART_SendByte &rArr; IOUART_PutChar
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_SetCurrent
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_StartTest
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_SetVoltage
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_PowerOn
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_RunAllTests
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_UpdateStatus
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_ProcessHeartbeat
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_UpdateStatus
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_ShowMessage
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_Init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_Init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IOUART_Init
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM2_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[ad]"></a>sum_array</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, test_board_control.o(.text.sum_array))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = sum_array
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_ValidatePacket
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_BuildPacket
</UL>

<P><STRONG><a name="[ca]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[e5]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[e6]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[e7]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[c6]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = printf
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_error_handling
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_display_functions
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_board_control_commands
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_packet_validation
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_uart_communication
</UL>

<P><STRONG><a name="[cc]"></a>__0snprintf</STRONG> (Thumb, 48 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[e8]"></a>__1snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[e9]"></a>__2snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[ea]"></a>__c89snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[76]"></a>snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_display_functions
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_uart_communication
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_SetComponent
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_RunAllTests
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_UpdateStatus
</UL>

<P><STRONG><a name="[eb]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[ec]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[ed]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[5a]"></a>fputc</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, fputc.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = fputc
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL><P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[9e]"></a>iouart_TXD</STRONG> (Thumb, 88 bytes, Stack size 4 bytes, main.o(.text.iouart_TXD))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = iouart_TXD
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
</UL>

<P><STRONG><a name="[a4]"></a>MX_TIM1_Init</STRONG> (Thumb, 134 bytes, Stack size 40 bytes, main.o(.text.MX_TIM1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = MX_TIM1_Init &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a7]"></a>MX_TIM2_Init</STRONG> (Thumb, 130 bytes, Stack size 40 bytes, main.o(.text.MX_TIM2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = MX_TIM2_Init &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a8]"></a>MX_TIM3_Init</STRONG> (Thumb, 132 bytes, Stack size 40 bytes, main.o(.text.MX_TIM3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = MX_TIM3_Init &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b0]"></a>update_board_info</STRONG> (Thumb, 142 bytes, Stack size 32 bytes, test_board_control.o(.text.update_board_info))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = update_board_info &rArr; TestBoard_GetStatusName
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_GetStatusName
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_EmergencyStop
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_StopTest
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_Reset
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_PowerOff
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_StartTest
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_PowerOn
</UL>

<P><STRONG><a name="[ba]"></a>test_uart_communication</STRONG> (Thumb, 128 bytes, Stack size 48 bytes, test_board_test.o(.text.test_uart_communication))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = test_uart_communication &rArr; snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IOUART_SendString
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;snprintf
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printf
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_RunAllTests
</UL>

<P><STRONG><a name="[bb]"></a>test_packet_validation</STRONG> (Thumb, 1022 bytes, Stack size 96 bytes, test_board_test.o(.text.test_packet_validation))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = test_packet_validation &rArr; TestBoard_BuildPacket &rArr; sum_array
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_ValidatePacket
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_BuildPacket
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printf
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_RunAllTests
</UL>

<P><STRONG><a name="[bc]"></a>test_board_control_commands</STRONG> (Thumb, 1520 bytes, Stack size 24 bytes, test_board_test.o(.text.test_board_control_commands))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = test_board_control_commands &rArr; TestBoard_PowerOffAll &rArr; TestBoard_PowerOff &rArr; TestBoard_BuildPacket &rArr; sum_array
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_GetInfo
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_PowerOffAll
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_PowerOnAll
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_StopTest
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_GetStatus
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_Reset
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_PowerOff
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_SetCurrent
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_StartTest
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_SetVoltage
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_PowerOn
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printf
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_RunAllTests
</UL>

<P><STRONG><a name="[bd]"></a>test_display_functions</STRONG> (Thumb, 278 bytes, Stack size 40 bytes, test_board_test.o(.text.test_display_functions))
<BR><BR>[Stack]<UL><LI>Max Depth = 364<LI>Call Chain = test_display_functions &rArr; Display_UpdateStatus &rArr; Display_SetComponent &rArr; Display_SendCommand &rArr; IOUART_SendByte &rArr; IOUART_PutChar
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_ShowBoardStatus
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_UpdateStatus
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_UpdateStatus
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Display_ShowMessage
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;snprintf
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printf
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_RunAllTests
</UL>

<P><STRONG><a name="[be]"></a>test_error_handling</STRONG> (Thumb, 532 bytes, Stack size 168 bytes, test_board_test.o(.text.test_error_handling))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = test_error_handling &rArr; TestBoard_EmergencyStop &rArr; TestBoard_StopTest &rArr; TestBoard_BuildPacket &rArr; sum_array
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_EmergencyStop
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_GetStatus
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_BuildPacket
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_PowerOn
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;printf
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_RunAllTests
</UL>

<P><STRONG><a name="[bf]"></a>print_test_summary</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, test_board_test.o(.text.print_test_summary))
<BR><BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TestBoard_RunAllTests
</UL>

<P><STRONG><a name="[93]"></a>TIM_TI1_ConfigInputStage</STRONG> (Thumb, 80 bytes, Stack size 20 bytes, stm32f2xx_hal_tim.o(.text.TIM_TI1_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_TI1_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[94]"></a>TIM_ITRx_SetConfig</STRONG> (Thumb, 42 bytes, Stack size 12 bytes, stm32f2xx_hal_tim.o(.text.TIM_ITRx_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_ITRx_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[95]"></a>TIM_TI2_ConfigInputStage</STRONG> (Thumb, 82 bytes, Stack size 20 bytes, stm32f2xx_hal_tim.o(.text.TIM_TI2_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_TI2_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[89]"></a>__NVIC_SetPriorityGrouping</STRONG> (Thumb, 60 bytes, Stack size 12 bytes, stm32f2xx_hal_cortex.o(.text.__NVIC_SetPriorityGrouping))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = __NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>

<P><STRONG><a name="[86]"></a>__NVIC_GetPriorityGrouping</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f2xx_hal_cortex.o(.text.__NVIC_GetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[87]"></a>NVIC_EncodePriority</STRONG> (Thumb, 108 bytes, Stack size 32 bytes, stm32f2xx_hal_cortex.o(.text.NVIC_EncodePriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = NVIC_EncodePriority
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[88]"></a>__NVIC_SetPriority</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, stm32f2xx_hal_cortex.o(.text.__NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Config
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[85]"></a>__NVIC_EnableIRQ</STRONG> (Thumb, 48 bytes, Stack size 4 bytes, stm32f2xx_hal_cortex.o(.text.__NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>

<P><STRONG><a name="[8d]"></a>SysTick_Config</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, stm32f2xx_hal_cortex.o(.text.SysTick_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SysTick_Config &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>

<P><STRONG><a name="[cd]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[cb]"></a>_printf_core</STRONG> (Thumb, 1744 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0snprintf
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[d0]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[cf]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[5b]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printfa.o(i._snputc))
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0snprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
