/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : test_board_control.h
  * @brief          : 测试板控制系统头文件
  ******************************************************************************
  * @attention
  *
  * 测试板控制规范与通信协议
  * 控制规范:
  * - 新建测试板控制函数系统，包含基础控制函数、参数设置函数、批量控制函数和高级功能
  * - 每个测试板通过独立的串口进行控制，支持并发操作
  * - 包含完整的错误处理和安全机制
  *
  ******************************************************************************
  */
/* USER CODE END Header */

#ifndef __TEST_BOARD_CONTROL_H
#define __TEST_BOARD_CONTROL_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdint.h>
#include <stdbool.h>

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/**
 * @brief 测试板类型枚举
 * 定义8种类型，分别对应不同的测试板和TEC板
 */
typedef enum {
    TEST_BOARD_EL1 = 0,    // 测试板1 - EL1
    TEST_BOARD_EL2 = 1,    // 测试板2 - EL2
    TEST_BOARD_EL3 = 2,    // 测试板3 - EL3
    TEST_BOARD_EL4 = 3,    // 测试板4 - EL4
    TEC_BOARD_1 = 4,       // TEC板1
    TEC_BOARD_2 = 5,       // TEC板2
    TEC_BOARD_3 = 6,       // TEC板3
    TEC_BOARD_4 = 7,       // TEC板4
    BOARD_COUNT = 8        // 总板卡数量
} TestBoardType_t;

/**
 * @brief 测试板控制指令枚举
 * 定义常用指令代码
 */
typedef enum {
    CMD_POWER_ON = 0x01,      // 上电指令
    CMD_POWER_OFF = 0x02,     // 断电指令
    CMD_RESET = 0x03,         // 复位指令
    CMD_START_TEST = 0x10,    // 开始测试指令
    CMD_STOP_TEST = 0x11,     // 停止测试指令
    CMD_GET_STATUS = 0x20,    // 获取状态指令
    CMD_SET_VOLTAGE = 0x30,   // 设置电压指令
    CMD_SET_CURRENT = 0x31,   // 设置电流指令
    CMD_READ_DATA = 0x40,     // 读取数据指令
    CMD_CALIBRATE = 0x50,     // 校准指令
    CMD_HEARTBEAT = 0xF0,     // 心跳指令
    CMD_ACK = 0xFE,           // 应答指令
    CMD_NACK = 0xFF           // 否定应答指令
} TestBoardCommand_t;

/**
 * @brief 测试板状态枚举
 */
typedef enum {
    BOARD_STATUS_OFFLINE = 0,     // 离线
    BOARD_STATUS_IDLE = 1,        // 空闲
    BOARD_STATUS_TESTING = 2,     // 测试中
    BOARD_STATUS_ERROR = 3,       // 错误状态
    BOARD_STATUS_CALIBRATING = 4  // 校准中
} TestBoardStatus_t;

/**
 * @brief 通信协议数据包格式
 * 标准的指令数据包格式，包含起始字符(0xAA)、板卡ID(0-7)、指令代码、数据长度和内容、校验和、结束字符(0x55)
 */
#define PACKET_START_BYTE    0xAA    // 起始字符
#define PACKET_END_BYTE      0x55    // 结束字符
#define PACKET_MAX_DATA_LEN  64      // 最大数据长度

typedef struct __attribute__((packed)) {
    uint8_t start_byte;              // 起始字节 0xAA
    uint8_t board_id;                // 板卡ID (0-7)
    uint8_t command;                 // 指令代码
    uint8_t data_length;             // 数据长度
    uint8_t data[PACKET_MAX_DATA_LEN]; // 数据内容
    uint8_t checksum;                // 校验和
    uint8_t end_byte;                // 结束字节 0x55
} TestBoardPacket_t;

/**
 * @brief 测试板信息结构体
 */
typedef struct {
    TestBoardType_t type;            // 板卡类型
    TestBoardStatus_t status;        // 当前状态
    uint8_t uart_id;                 // 对应的串口ID
    uint32_t last_heartbeat_time;    // 最后心跳时间
    uint16_t voltage_mv;             // 当前电压(mV)
    uint16_t current_ma;             // 当前电流(mA)
    uint32_t test_count;             // 测试次数
    uint32_t error_count;            // 错误次数
    bool communication_ok;           // 通信状态
} TestBoardInfo_t;

/**
 * @brief 串口屏显示信息结构体
 */
typedef struct {
    char board_status[BOARD_COUNT][16];  // 各板卡状态文本
    uint32_t system_time;                // 系统时间
    uint16_t total_tests;                // 总测试数
    uint16_t total_errors;               // 总错误数
    bool display_updated;                // 显示更新标志
} DisplayInfo_t;

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

// 大彩串口屏通信参数 (型号: DC48270M043_1111_0C, 4.3寸电容触摸屏)
#define DISPLAY_UART_ID          8       // 大彩串口屏使用UART8
#define DISPLAY_BAUD_RATE        115200  // 波特率 (115200bps)
#define DISPLAY_UPDATE_INTERVAL  1000    // 显示更新间隔(ms)

// 测试板通信超时设置
#define BOARD_COMM_TIMEOUT       3000    // 通信超时时间(ms)
#define HEARTBEAT_INTERVAL       5000    // 心跳间隔时间(ms)

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

// 校验和计算宏
#define CALCULATE_CHECKSUM(packet) \
    ((uint8_t)(~((packet)->board_id + (packet)->command + (packet)->data_length + \
    sum_array((packet)->data, (packet)->data_length)) + 1))

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/

/* 基础控制函数 */
bool TestBoard_Init(void);
bool TestBoard_PowerOn(TestBoardType_t board_type);
bool TestBoard_PowerOff(TestBoardType_t board_type);
bool TestBoard_Reset(TestBoardType_t board_type);
TestBoardStatus_t TestBoard_GetStatus(TestBoardType_t board_type);

/* 参数设置函数 */
bool TestBoard_SetVoltage(TestBoardType_t board_type, uint16_t voltage_mv);
bool TestBoard_SetCurrent(TestBoardType_t board_type, uint16_t current_ma);
bool TestBoard_Calibrate(TestBoardType_t board_type);

/* 测试控制函数 */
bool TestBoard_StartTest(TestBoardType_t board_type);
bool TestBoard_StopTest(TestBoardType_t board_type);
bool TestBoard_ReadData(TestBoardType_t board_type, uint8_t *data, uint8_t *len);

/* 批量控制函数 */
bool TestBoard_PowerOnAll(void);
bool TestBoard_PowerOffAll(void);
bool TestBoard_ResetAll(void);
bool TestBoard_StartTestAll(void);
bool TestBoard_StopTestAll(void);

/* 高级功能 */
bool TestBoard_SendCustomCommand(TestBoardType_t board_type, uint8_t cmd, uint8_t *data, uint8_t len);
bool TestBoard_UpdateFirmware(TestBoardType_t board_type, uint8_t *firmware_data, uint32_t len);

/* 通信协议函数 */
bool TestBoard_SendPacket(TestBoardType_t board_type, TestBoardPacket_t *packet);
bool TestBoard_ReceivePacket(TestBoardType_t board_type, TestBoardPacket_t *packet, uint32_t timeout_ms);
bool TestBoard_BuildPacket(TestBoardPacket_t *packet, uint8_t board_id, uint8_t cmd, uint8_t *data, uint8_t len);
bool TestBoard_ValidatePacket(TestBoardPacket_t *packet);

/* 状态管理函数 */
void TestBoard_UpdateStatus(void);
void TestBoard_ProcessHeartbeat(void);
TestBoardInfo_t* TestBoard_GetInfo(TestBoardType_t board_type);

/* 大彩串口屏控制函数 */
bool Display_Init(void);
void Display_UpdateStatus(void);
void Display_ShowMessage(const char *message);
void Display_ShowBoardStatus(TestBoardType_t board_type, const char *status);
void Display_SendCommand(const char *cmd);
void Display_SetPage(uint8_t page);
void Display_SetComponent(const char *component, const char *attribute, const char *value);

/* 错误处理和安全机制 */
void TestBoard_ErrorHandler(TestBoardType_t board_type, uint32_t error_code);
bool TestBoard_SafetyCheck(TestBoardType_t board_type);
void TestBoard_EmergencyStop(void);

/* 工具函数 */
uint8_t sum_array(uint8_t *array, uint8_t len);
const char* TestBoard_GetTypeName(TestBoardType_t type);
const char* TestBoard_GetStatusName(TestBoardStatus_t status);

/* 测试函数 */
bool TestBoard_RunAllTests(void);
void TestBoard_ResetTestResult(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __TEST_BOARD_CONTROL_H */