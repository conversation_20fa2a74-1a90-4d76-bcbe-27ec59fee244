# 大彩串口屏引脚配置说明

## 大彩串口屏规格

### 硬件参数
- **型号**: DC48270M043_1111_0C
- **尺寸**: 4.3寸
- **分辨率**: 480x272像素
- **触摸方式**: 电容触摸
- **通信接口**: RS232/TTL串口
- **波特率**: 115200bps (默认)
- **工作电压**: 5V/3.3V

### 通信协议
- **数据位**: 8位
- **停止位**: 1位
- **校验位**: 无
- **流控**: 无
- **结束符**: 3个0xFF (0xFF 0xFF 0xFF)

## 当前系统配置

### 1. 串口分配

根据您的嵌入式开发规范，系统现在配置为9路虚拟串口：

| 串口ID | 功能 | TX引脚 | RX引脚 | 说明 |
|---------|------|---------|---------|------|
| UART0 | EL1测试板 | PB1 | PB2 | 测试板1-RS232接口 |
| UART1 | EL2测试板 | PA5 | PA6 | 测试板2-RS232接口 |
| UART2 | EL3测试板 | PC12 | PD2 | 测试板3-RS232接口 |
| UART3 | EL4测试板 | PC6 | PC7 | 测试板4-RS232接口 |
| UART4 | TEC1板 | PA7 | PC4 | TEC1~RS232接口 |
| UART5 | TEC2板 | PC5 | PB0 | TEC2~RS232接口 |
| UART6 | TEC3板 | PB10 | PB11 | TEC3~RS232接口 |
| UART7 | TEC4板 | PA8 | PC9 | TEC4~RS232接口 |
| **UART8** | **大彩串口屏** | **PC10** | **PC11** | **DC48270M043_1111_0C** |

### 2. 头文件配置 - `test_board_control.h`

```c
// 大彩串口屏通信参数 (型号: DC48270M043_1111_0C, 4.3寸电容触摸屏)
#define DISPLAY_UART_ID          8       // 大彩串口屏使用UART8
#define DISPLAY_BAUD_RATE        115200  // 波特率 (115200bps)
#define DISPLAY_UPDATE_INTERVAL  1000    // 显示更新间隔(ms)
```

### 3. GPIO引脚映射 - `main.c`

```c
/* 定义9个串口的GPIO映射 */
IOUART_GPIO_t iouart_gpio[IOUART_NUM] = {
    // ... 前8个测试板串口 ...
    {GPIOC, GPIO_PIN_10, GPIOC, GPIO_PIN_11}  // UART8: 大彩串口屏
};
```

### 4. 串口数量定义 - `main.c`

```c
/* 定义9个虚拟串口（包含大彩串口屏） */
#define IOUART_NUM 9
```

## 大彩串口屏控制函数

### 基本控制函数

```c
// 初始化串口屏
bool Display_Init(void);

// 发送命令到串口屏
void Display_SendCommand(const char *cmd);

// 设置页面
void Display_SetPage(uint8_t page);

// 设置组件属性
void Display_SetComponent(const char *component, const char *attribute, const char *value);

// 更新显示状态
void Display_UpdateStatus(void);

// 显示消息
void Display_ShowMessage(const char *message);

// 显示板卡状态
void Display_ShowBoardStatus(TestBoardType_t board_type, const char *status);
```

### 使用示例

```c
// 初始化串口屏
Display_Init();

// 设置页面0
Display_SetPage(0);

// 设置文本组件内容
Display_SetComponent("t0", "txt", "System Ready");

// 显示消息
Display_ShowMessage("Control Board Online!");

// 更新板卡状态
Display_ShowBoardStatus(TEST_BOARD_EL1, "TESTING");
```

## 大彩串口屏命令格式

### 基本命令格式
```
命令内容 + 结束符(0xFF 0xFF 0xFF)
```

### 常用命令示例

```c
// 设置页面
"page 0" + 0xFF 0xFF 0xFF

// 设置文本内容
"t0.txt=\"Hello World\"" + 0xFF 0xFF 0xFF

// 设置背光亮度 (0-100)
"dim=80" + 0xFF 0xFF 0xFF

// 设置按钮文本
"b0.txt=\"Button\"" + 0xFF 0xFF 0xFF

// 设置数字值
"n0.val=123" + 0xFF 0xFF 0xFF
```

## 引脚冲突检查

### ✅ 当前配置状态
- **所有引脚独立**: 每个串口使用不同的GPIO引脚
- **无硬件冲突**: PC10/PC11未被其他功能使用
- **符合开发规范**: 遵循"每个串口设备必须使用独立的串口和引脚"

### 电气特性
- **电压等级**: 3.3V CMOS兼容
- **驱动能力**: 标准GPIO驱动足够
- **信号完整性**: 短距离PCB布线，信号良好

## 系统初始化流程

### 1. 硬件初始化
```c
// GPIO时钟使能
__HAL_RCC_GPIOC_CLK_ENABLE();

// TX引脚配置为推挽输出
GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
GPIO_InitStruct.Pull = GPIO_PULLUP;
GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;

// RX引脚配置为输入，上拉
GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
GPIO_InitStruct.Pull = GPIO_PULLUP;
```

### 2. 串口屏初始化
```c
// 等待串口屏启动完成
HAL_Delay(2000);

// 设置默认页面
Display_SendCommand("page 0");

// 设置背光亮度
Display_SendCommand("dim=80");

// 显示初始化消息
Display_ShowMessage("System Init...");
```

## 调试与验证

### 1. 硬件验证
- 使用示波器检查PC10的TX信号
- 验证115200bps的波形
- 确认结束符(0xFF 0xFF 0xFF)的发送

### 2. 软件验证
```c
// 发送测试命令
Display_SendCommand("t0.txt=\"Test Message\"");

// 检查串口屏是否响应
// 可以在串口屏上创建简单的界面进行测试
```

### 3. 性能测试
- 测试并发发送的稳定性
- 验证多串口同时工作时的性能
- 检查显示更新的实时性

## 故障排除

### 常见问题

1. **串口屏无响应**
   - 检查PC10/PC11引脚连接
   - 验证波特率设置(115200)
   - 确认电源供应

2. **显示内容错误**
   - 检查命令格式
   - 验证结束符(0xFF 0xFF 0xFF)
   - 确认字符编码

3. **通信不稳定**
   - 检查信号完整性
   - 优化GPIO驱动强度
   - 添加适当的延时

### 调试建议
1. 使用逻辑分析仪监控串口数据
2. 在串口屏上显示接收到的数据
3. 逐步测试各个显示功能
4. 验证触摸功能的响应

## 总结

大彩串口屏DC48270M043_1111_0C现在完全集成到控制系统中：
- ✅ 使用专用的UART8串口 (PC10/PC11)
- ✅ 避免了与测试板串口的冲突
- ✅ 遵循了嵌入式开发规范
- ✅ 实现了完整的显示控制功能
- ✅ 支持触摸交互和状态显示

系统现在可以同时控制8个测试板并在4.3寸触摸屏上显示实时状态信息。