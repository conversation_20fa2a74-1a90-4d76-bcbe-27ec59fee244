T77BC 000:134.354   SEGGER J-Link V7.96h Log File
T77BC 000:134.701   DLL Compiled: May 15 2024 15:33:59
T77BC 000:134.717   Logging started @ 2025-08-26 08:20
T77BC 000:134.735   Process: D:\Keil\UV4\UV4.exe
T77BC 000:134.763 - 134.757ms
T77BC 000:134.791 JLINK_SetWarnOutHandler(...)
T77BC 000:134.813 - 0.023ms
T77BC 000:134.839 JLINK_OpenEx(...)
T77BC 000:138.540   Firmware: J-Link ARM V8 compiled Nov 28 2014 13:44:46
T77BC 000:140.122   Firmware: J-Link ARM V8 compiled Nov 28 2014 13:44:46
T77BC 000:143.865   Hardware: V8.00
T77BC 000:143.888   S/N: 805251123
T77BC 000:143.909   OEM: SEGGER
T77BC 000:143.930   Feature(s): R<PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,GDB
T77BC 000:145.333   Bootloader: (Could not read)
T77BC 000:147.160   TELNET listener socket opened on port 19021
T77BC 000:147.250   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T77BC 000:147.418   WEBSRV Webserver running on local port 19080
T77BC 000:147.531   Looking for J-Link GUI Server exe at: D:\Keil\ARM\Segger\JLinkGUIServer.exe
T77BC 000:147.720   Looking for J-Link GUI Server exe at: \JLinkGUIServer.exe
T77BC 000:453.305   Failed to connect to J-Link GUI Server.
T77BC 000:455.508 - 318.578ms returns "O.K."
T77BC 000:455.640 JLINK_GetEmuCaps()
T77BC 000:455.697 - 0.052ms returns 0xB9FF7BBF
T77BC 000:455.780 JLINK_TIF_GetAvailable(...)
T77BC 000:456.490 - 0.712ms
T77BC 000:456.573 JLINK_SetErrorOutHandler(...)
T77BC 000:456.624 - 0.050ms
T77BC 000:457.198 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\22\MDK-ARM\JLinkSettings.ini"", ...). 
T77BC 000:480.269 - 23.558ms returns 0x00
T77BC 000:480.309 JLINK_ExecCommand("Device = STM32F205RGTx", ...). 
T77BC 000:481.009   Flash bank @ 0x08000000: SFL: Parsing sectorization info from ELF file
T77BC 000:481.040     FlashDevice.SectorInfo[0]: .SectorSize = 0x00004000, .SectorStartAddr = 0x00000000
T77BC 000:481.061     FlashDevice.SectorInfo[1]: .SectorSize = 0x00010000, .SectorStartAddr = 0x00010000
T77BC 000:481.081     FlashDevice.SectorInfo[2]: .SectorSize = 0x00020000, .SectorStartAddr = 0x00020000
T77BC 000:481.103   FlashBank @0x08000000: Sectorization info from SFL ELF file ignored because sectorization override from DLL / XML file is active.
T77BC 000:482.576   Device "STM32F205RG" selected.
T77BC 000:482.867 - 2.542ms returns 0x00
T77BC 000:482.890 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T77BC 000:482.916 - 0.010ms returns 0x01
T77BC 000:482.932 JLINK_GetHardwareVersion()
T77BC 000:482.946 - 0.014ms returns 80000
T77BC 000:482.961 JLINK_GetDLLVersion()
T77BC 000:482.975 - 0.014ms returns 79608
T77BC 000:482.990 JLINK_GetOEMString(...)
T77BC 000:483.010 JLINK_GetFirmwareString(...)
T77BC 000:483.034 - 0.029ms
T77BC 000:483.084 JLINK_GetDLLVersion()
T77BC 000:483.099 - 0.015ms returns 79608
T77BC 000:483.114 JLINK_GetCompileDateTime()
T77BC 000:483.127 - 0.013ms
T77BC 000:483.143 JLINK_GetFirmwareString(...)
T77BC 000:483.157 - 0.013ms
T77BC 000:483.172 JLINK_GetHardwareVersion()
T77BC 000:483.186 - 0.013ms returns 80000
T77BC 000:483.201 JLINK_GetSN()
T77BC 000:483.216 - 0.015ms returns 805251123
T77BC 000:483.231 JLINK_GetOEMString(...)
T77BC 000:483.247 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T77BC 000:484.765 - 1.517ms returns 0x00
T77BC 000:484.786 JLINK_HasError()
T77BC 000:484.807 JLINK_SetSpeed(5000)
T77BC 000:485.096 - 0.290ms
T77BC 000:485.312 JLINK_HasError()
T77BC 000:485.331 JLINK_SetResetType(JLINKARM_RESET_TYPE_NORMAL)
T77BC 000:485.346 - 0.014ms returns JLINKARM_RESET_TYPE_NORMAL
T77BC 000:485.362 JLINK_Reset()
T77BC 000:485.824   InitTarget() start
T77BC 000:485.847    J-Link Script File: Executing InitTarget()
T77BC 000:486.986   SWD selected. Executing JTAG -> SWD switching sequence.
T77BC 000:490.364   Error: Failed to initialized DAP.
T77BC 000:490.389   Can not attach to CPU. Trying connect under reset.
T77BC 000:541.538   SWD selected. Executing JTAG -> SWD switching sequence.
T77BC 000:546.021   Error: Failed to initialized DAP.
T77BC 000:546.066   Connecting to CPU via connect under reset failed.
T77BC 000:597.851   InitTarget() end - Took 111ms
T77BC 000:599.408   Connect failed. Resetting via Reset pin and trying again.
T77BC 000:662.406   InitTarget() start
T77BC 000:662.549    J-Link Script File: Executing InitTarget()
T77BC 000:664.000   SWD selected. Executing JTAG -> SWD switching sequence.
T77BC 000:668.703   Error: Failed to initialized DAP.
T77BC 000:668.735   Can not attach to CPU. Trying connect under reset.
T77BC 000:719.867   SWD selected. Executing JTAG -> SWD switching sequence.
T77BC 000:724.922   Error: Failed to initialized DAP.
T77BC 000:724.976   Connecting to CPU via connect under reset failed.
T77BC 000:777.038   InitTarget() end - Took 114ms
T77BC 000:777.175 - 291.812ms
T77BC 000:777.240 JLINK_GetId()
T77BC 000:777.991   InitTarget() start
T77BC 000:778.088    J-Link Script File: Executing InitTarget()
T77BC 000:779.392   SWD selected. Executing JTAG -> SWD switching sequence.
T77BC 000:783.653   Error: Failed to initialized DAP.
T77BC 000:783.684   Can not attach to CPU. Trying connect under reset.
T77BC 000:835.211   SWD selected. Executing JTAG -> SWD switching sequence.
T77BC 000:840.339   Error: Failed to initialized DAP.
T77BC 000:840.392   Connecting to CPU via connect under reset failed.
T77BC 000:892.838   InitTarget() end - Took 114ms
T77BC 000:892.957   Connect failed. Resetting via Reset pin and trying again.
T77BC 000:956.290   InitTarget() start
T77BC 000:956.350    J-Link Script File: Executing InitTarget()
T77BC 000:957.784   SWD selected. Executing JTAG -> SWD switching sequence.
T77BC 000:962.473   Error: Failed to initialized DAP.
T77BC 000:962.507   Can not attach to CPU. Trying connect under reset.
T77BC 001:013.449   SWD selected. Executing JTAG -> SWD switching sequence.
T77BC 001:019.827   Error: Failed to initialized DAP.
T77BC 001:019.881   Connecting to CPU via connect under reset failed.
T77BC 001:072.109   InitTarget() end - Took 115ms
T77BC 001:072.234 - 294.993ms returns 0x00000000
T77BC 001:072.316 JLINK_GetId()
T77BC 001:073.360   InitTarget() start
T77BC 001:073.464    J-Link Script File: Executing InitTarget()
T77BC 001:075.014   SWD selected. Executing JTAG -> SWD switching sequence.
T77BC 001:079.297   Error: Failed to initialized DAP.
T77BC 001:079.325   Can not attach to CPU. Trying connect under reset.
T77BC 001:130.416   SWD selected. Executing JTAG -> SWD switching sequence.
T77BC 001:135.936   Error: Failed to initialized DAP.
T77BC 001:135.978   Connecting to CPU via connect under reset failed.
T77BC 001:187.255   InitTarget() end - Took 113ms
T77BC 001:187.308   Connect failed. Resetting via Reset pin and trying again.
T77BC 001:250.056   InitTarget() start
T77BC 001:250.190    J-Link Script File: Executing InitTarget()
T77BC 001:251.984   SWD selected. Executing JTAG -> SWD switching sequence.
T77BC 001:257.009   Error: Failed to initialized DAP.
T77BC 001:257.042   Can not attach to CPU. Trying connect under reset.
T77BC 001:308.266   SWD selected. Executing JTAG -> SWD switching sequence.
T77BC 001:312.673   Error: Failed to initialized DAP.
T77BC 001:312.727   Connecting to CPU via connect under reset failed.
T77BC 001:364.778   InitTarget() end - Took 114ms
T77BC 001:364.903 - 292.585ms returns 0x00000000
T77BC 001:370.232 JLINK_GetFirmwareString(...)
T77BC 001:370.271 - 0.039ms
T77BC 185:782.064 JLINK_Close()
T77BC 185:795.616 - 13.552ms
T77BC 185:795.664   
T77BC 185:795.680   Closed
