T2180 000:004.556   SEGGER J-Link V7.96h Log File
T2180 000:004.686   DLL Compiled: May 15 2024 15:33:59
T2180 000:004.709   Logging started @ 2025-08-27 08:17
T2180 000:004.731   Process: D:\Keil\UV4\UV4.exe
T2180 000:004.753 - 4.751ms
T2180 000:004.777 JLINK_SetWarnOutHandler(...)
T2180 000:004.797 - 0.021ms
T2180 000:004.818 JLINK_OpenEx(...)
T2180 000:006.702   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T2180 000:007.238   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T2180 000:007.382   Decompressing FW timestamp took 107 us
T2180 000:014.771   Hardware: V9.40
T2180 000:014.803   S/N: 69400832
T2180 000:014.823   OEM: SEGGER
T2180 000:014.844   Feature(s): <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, J<PERSON><PERSON>
T2180 000:015.316   Bootloader: (Could not read)
T2180 000:015.966   TELNET listener socket opened on port 19021
T2180 000:016.064   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T2180 000:016.249   WEBSRV Webserver running on local port 19080
T2180 000:016.376   Looking for J-Link GUI Server exe at: D:\Keil\ARM\Segger\JLinkGUIServer.exe
T2180 000:016.572   Looking for J-Link GUI Server exe at: \JLinkGUIServer.exe
T2180 000:326.704   Failed to connect to J-Link GUI Server.
T2180 000:326.776 - 321.952ms returns "O.K."
T2180 000:326.800 JLINK_GetEmuCaps()
T2180 000:326.817 - 0.015ms returns 0xB9FF7BBF
T2180 000:326.835 JLINK_TIF_GetAvailable(...)
T2180 000:327.010 - 0.176ms
T2180 000:327.027 JLINK_SetErrorOutHandler(...)
T2180 000:327.041 - 0.014ms
T2180 000:327.068 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\22\MDK-ARM\JLinkSettings.ini"", ...). 
T2180 000:338.221 - 11.152ms returns 0x00
T2180 000:340.929 JLINK_ExecCommand("Device = STM32F205RGTx", ...). 
T2180 000:341.643   Flash bank @ 0x08000000: SFL: Parsing sectorization info from ELF file
T2180 000:341.681     FlashDevice.SectorInfo[0]: .SectorSize = 0x00004000, .SectorStartAddr = 0x00000000
T2180 000:341.702     FlashDevice.SectorInfo[1]: .SectorSize = 0x00010000, .SectorStartAddr = 0x00010000
T2180 000:341.723     FlashDevice.SectorInfo[2]: .SectorSize = 0x00020000, .SectorStartAddr = 0x00020000
T2180 000:341.745   FlashBank @0x08000000: Sectorization info from SFL ELF file ignored because sectorization override from DLL / XML file is active.
T2180 000:343.613   Device "STM32F205RG" selected.
T2180 000:343.977 - 3.000ms returns 0x00
T2180 000:343.999 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T2180 000:344.017 - 0.001ms returns 0x01
T2180 000:344.033 JLINK_GetHardwareVersion()
T2180 000:344.048 - 0.014ms returns 94000
T2180 000:344.062 JLINK_GetDLLVersion()
T2180 000:344.076 - 0.013ms returns 79608
T2180 000:344.091 JLINK_GetOEMString(...)
T2180 000:344.106 JLINK_GetFirmwareString(...)
T2180 000:344.120 - 0.014ms
T2180 000:348.310 JLINK_GetDLLVersion()
T2180 000:348.349 - 0.038ms returns 79608
T2180 000:348.364 JLINK_GetCompileDateTime()
T2180 000:348.378 - 0.014ms
T2180 000:349.544 JLINK_GetFirmwareString(...)
T2180 000:349.574 - 0.030ms
T2180 000:350.667 JLINK_GetHardwareVersion()
T2180 000:350.692 - 0.025ms returns 94000
T2180 000:352.065 JLINK_GetSN()
T2180 000:352.089 - 0.024ms returns 69400832
T2180 000:353.381 JLINK_GetOEMString(...)
T2180 000:355.594 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T2180 000:356.108 - 0.514ms returns 0x00
T2180 000:356.127 JLINK_HasError()
T2180 000:356.148 JLINK_SetSpeed(5000)
T2180 000:356.209 - 0.062ms
T2180 000:356.226 JLINK_GetId()
T2180 000:357.864   InitTarget() start
T2180 000:357.908    J-Link Script File: Executing InitTarget()
T2180 000:359.550   SWD selected. Executing JTAG -> SWD switching sequence.
T2180 000:362.502   DAP initialized successfully.
T2180 000:367.728   InitTarget() end - Took 8.55ms
T2180 000:369.376   Found SW-DP with ID 0x2BA01477
T2180 000:372.653   DPIDR: 0x2BA01477
T2180 000:374.061   CoreSight SoC-400 or earlier
T2180 000:375.316   Scanning AP map to find all available APs
T2180 000:377.345   AP[1]: Stopped AP scan as end of AP map has been reached
T2180 000:378.637   AP[0]: AHB-AP (IDR: 0x24770011)
T2180 000:379.823   Iterating through AP map to find AHB-AP to use
T2180 000:381.929   AP[0]: Core found
T2180 000:383.043   AP[0]: AHB-AP ROM base: 0xE00FF000
T2180 000:384.657   CPUID register: 0x412FC230. Implementer code: 0x41 (ARM)
T2180 000:385.919   Found Cortex-M3 r2p0, Little endian.
T2180 000:386.211   -- Max. mem block: 0x00010E60
T2180 000:386.577   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2180 000:386.868   CPU_ReadMem(4 bytes @ 0x********)
T2180 000:388.390   FPUnit: 6 code (BP) slots and 2 literal slots
T2180 000:388.420   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T2180 000:388.716   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2180 000:389.002   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:389.269   CPU_WriteMem(4 bytes @ 0xE0001000)
T2180 000:389.559   CPU_ReadMem(4 bytes @ 0xE000ED88)
T2180 000:389.853   CPU_WriteMem(4 bytes @ 0xE000ED88)
T2180 000:390.265   CPU_ReadMem(4 bytes @ 0xE000ED88)
T2180 000:390.538   CPU_WriteMem(4 bytes @ 0xE000ED88)
T2180 000:392.117   CoreSight components:
T2180 000:393.402   ROMTbl[0] @ E00FF000
T2180 000:393.437   CPU_ReadMem(64 bytes @ 0xE00FF000)
T2180 000:394.066   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T2180 000:395.833   [0][0]: E000E000 CID B105E00D PID 002BB000 SCS
T2180 000:395.866   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T2180 000:397.551   [0][1]: E0001000 CID B105E00D PID 002BB002 DWT
T2180 000:397.580   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T2180 000:399.176   [0][2]: ******** CID B105E00D PID 002BB003 FPB
T2180 000:399.204   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T2180 000:400.803   [0][3]: ******** CID B105E00D PID 002BB001 ITM
T2180 000:400.832   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T2180 000:402.499   [0][4]: ******** CID B105900D PID 002BB923 TPIU-Lite
T2180 000:402.529   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T2180 000:404.320   [0][5]: ******** CID B105900D PID 002BB924 ETM-M3
T2180 000:404.553 - 48.327ms returns 0x2BA01477
T2180 000:404.603 JLINK_GetDLLVersion()
T2180 000:404.619 - 0.016ms returns 79608
T2180 000:404.635 JLINK_CORE_GetFound()
T2180 000:404.649 - 0.014ms returns 0x30000FF
T2180 000:404.664 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T2180 000:404.680   Value=0xE00FF000
T2180 000:404.701 - 0.037ms returns 0
T2180 000:406.359 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T2180 000:406.385   Value=0xE00FF000
T2180 000:406.406 - 0.047ms returns 0
T2180 000:406.421 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T2180 000:406.435   Value=0x********
T2180 000:406.456 - 0.035ms returns 0
T2180 000:406.471 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
T2180 000:406.499   CPU_ReadMem(32 bytes @ 0xE0041FD0)
T2180 000:406.970   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T2180 000:406.993 - 0.522ms returns 32 (0x20)
T2180 000:407.009 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T2180 000:407.023   Value=0x00000000
T2180 000:407.043 - 0.034ms returns 0
T2180 000:407.058 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T2180 000:407.073   Value=0x********
T2180 000:407.093 - 0.034ms returns 0
T2180 000:407.108 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T2180 000:407.122   Value=0x********
T2180 000:407.142 - 0.034ms returns 0
T2180 000:407.157 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T2180 000:407.171   Value=0xE0001000
T2180 000:407.192 - 0.035ms returns 0
T2180 000:407.207 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T2180 000:407.221   Value=0x********
T2180 000:407.241 - 0.034ms returns 0
T2180 000:407.256 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T2180 000:407.270   Value=0xE000E000
T2180 000:407.291 - 0.034ms returns 0
T2180 000:407.305 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T2180 000:407.320   Value=0xE000EDF0
T2180 000:407.342 - 0.036ms returns 0
T2180 000:407.358 JLINK_GetDebugInfo(0x01 = Unknown)
T2180 000:407.372   Value=0x00000000
T2180 000:407.393 - 0.035ms returns 0
T2180 000:407.409 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T2180 000:407.429   CPU_ReadMem(4 bytes @ 0xE000ED00)
T2180 000:407.693   Data:  30 C2 2F 41
T2180 000:407.716   Debug reg: CPUID
T2180 000:407.737 - 0.327ms returns 1 (0x1)
T2180 000:407.752 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T2180 000:407.766   Value=0x00000000
T2180 000:407.787 - 0.034ms returns 0
T2180 000:407.802 JLINK_HasError()
T2180 000:407.817 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T2180 000:407.832 - 0.014ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T2180 000:407.847 JLINK_Reset()
T2180 000:407.866   CPU is running
T2180 000:407.888   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T2180 000:408.210   CPU is running
T2180 000:408.232   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2180 000:410.166   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T2180 000:411.859   Reset: Reset device via AIRCR.SYSRESETREQ.
T2180 000:411.895   CPU is running
T2180 000:411.920   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T2180 000:464.559   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2180 000:464.949   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2180 000:467.889   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2180 000:473.587   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2180 000:476.557   CPU_WriteMem(4 bytes @ 0x********)
T2180 000:476.905   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T2180 000:477.232   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:477.555 - 69.707ms
T2180 000:477.605 JLINK_Halt()
T2180 000:477.620 - 0.015ms returns 0x00
T2180 000:477.636 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T2180 000:477.655   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2180 000:478.135   Data:  03 00 03 00
T2180 000:478.157   Debug reg: DHCSR
T2180 000:478.178 - 0.542ms returns 1 (0x1)
T2180 000:478.193 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T2180 000:478.208   Debug reg: DHCSR
T2180 000:478.385   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T2180 000:478.709 - 0.515ms returns 0 (0x00000000)
T2180 000:478.728 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T2180 000:478.742   Debug reg: DEMCR
T2180 000:478.765   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2180 000:479.072 - 0.343ms returns 0 (0x00000000)
T2180 000:484.099 JLINK_GetHWStatus(...)
T2180 000:484.288 - 0.190ms returns 0
T2180 000:487.903 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T2180 000:487.943 - 0.039ms returns 0x06
T2180 000:487.959 JLINK_GetNumBPUnits(Type = 0xF0)
T2180 000:487.973 - 0.014ms returns 0x2000
T2180 000:487.987 JLINK_GetNumWPUnits()
T2180 000:488.003 - 0.015ms returns 4
T2180 000:492.779 JLINK_GetSpeed()
T2180 000:492.821 - 0.041ms returns 4000
T2180 000:495.107 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T2180 000:495.147   CPU_ReadMem(4 bytes @ 0xE000E004)
T2180 000:495.463   Data:  02 00 00 00
T2180 000:495.486 - 0.378ms returns 1 (0x1)
T2180 000:495.501 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T2180 000:495.517   CPU_ReadMem(4 bytes @ 0xE000E004)
T2180 000:495.789   Data:  02 00 00 00
T2180 000:495.810 - 0.309ms returns 1 (0x1)
T2180 000:495.826 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T2180 000:495.840   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T2180 000:495.865   CPU_WriteMem(28 bytes @ 0xE0001000)
T2180 000:496.350 - 0.524ms returns 0x1C
T2180 000:496.369 JLINK_Halt()
T2180 000:496.383 - 0.014ms returns 0x00
T2180 000:496.398 JLINK_IsHalted()
T2180 000:496.413 - 0.014ms returns TRUE
T2180 000:520.284 JLINK_WriteMem(0x20000000, 0x180 Bytes, ...)
T2180 000:520.314   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T2180 000:520.527   CPU_WriteMem(384 bytes @ 0x20000000)
T2180 000:522.914 - 2.630ms returns 0x180
T2180 000:522.969 JLINK_HasError()
T2180 000:522.990 JLINK_WriteReg(R0, 0x08000000)
T2180 000:523.007 - 0.021ms returns 0
T2180 000:523.022 JLINK_WriteReg(R1, 0x017D7840)
T2180 000:523.036 - 0.014ms returns 0
T2180 000:523.051 JLINK_WriteReg(R2, 0x00000001)
T2180 000:523.065 - 0.013ms returns 0
T2180 000:523.079 JLINK_WriteReg(R3, 0x00000000)
T2180 000:523.094 - 0.014ms returns 0
T2180 000:523.108 JLINK_WriteReg(R4, 0x00000000)
T2180 000:523.123 - 0.014ms returns 0
T2180 000:523.141 JLINK_WriteReg(R5, 0x00000000)
T2180 000:523.156 - 0.015ms returns 0
T2180 000:523.171 JLINK_WriteReg(R6, 0x00000000)
T2180 000:523.185 - 0.014ms returns 0
T2180 000:523.200 JLINK_WriteReg(R7, 0x00000000)
T2180 000:523.214 - 0.014ms returns 0
T2180 000:523.228 JLINK_WriteReg(R8, 0x00000000)
T2180 000:523.242 - 0.014ms returns 0
T2180 000:523.257 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:523.271 - 0.014ms returns 0
T2180 000:523.286 JLINK_WriteReg(R10, 0x00000000)
T2180 000:523.300 - 0.014ms returns 0
T2180 000:523.314 JLINK_WriteReg(R11, 0x00000000)
T2180 000:523.328 - 0.014ms returns 0
T2180 000:523.344 JLINK_WriteReg(R12, 0x00000000)
T2180 000:523.358 - 0.014ms returns 0
T2180 000:523.373 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:523.388 - 0.015ms returns 0
T2180 000:523.403 JLINK_WriteReg(R14, 0x20000001)
T2180 000:523.417 - 0.014ms returns 0
T2180 000:523.687 JLINK_WriteReg(R15 (PC), 0x20000054)
T2180 000:523.704 - 0.272ms returns 0
T2180 000:523.719 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:523.733 - 0.014ms returns 0
T2180 000:523.748 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:523.762 - 0.014ms returns 0
T2180 000:523.777 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:523.791 - 0.014ms returns 0
T2180 000:523.805 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:523.819 - 0.014ms returns 0
T2180 000:523.835 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:523.854   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:524.216 - 0.381ms returns 0x00000001
T2180 000:524.233 JLINK_Go()
T2180 000:524.249   CPU_WriteMem(2 bytes @ 0x20000000)
T2180 000:524.564   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:524.845   CPU_WriteMem(4 bytes @ 0xE0002008)
T2180 000:524.868   CPU_WriteMem(4 bytes @ 0xE000200C)
T2180 000:524.888   CPU_WriteMem(4 bytes @ 0xE0002010)
T2180 000:524.909   CPU_WriteMem(4 bytes @ 0xE0002014)
T2180 000:524.930   CPU_WriteMem(4 bytes @ 0xE0002018)
T2180 000:524.951   CPU_WriteMem(4 bytes @ 0xE000201C)
T2180 000:526.288   CPU_WriteMem(4 bytes @ 0xE0001004)
T2180 000:530.916   Memory map 'after startup completion point' is active
T2180 000:530.953 - 6.719ms
T2180 000:530.972 JLINK_IsHalted()
T2180 000:533.429   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:533.685 - 2.712ms returns TRUE
T2180 000:533.703 JLINK_ReadReg(R15 (PC))
T2180 000:533.719 - 0.015ms returns 0x20000000
T2180 000:533.735 JLINK_ClrBPEx(BPHandle = 0x00000001)
T2180 000:533.750 - 0.015ms returns 0x00
T2180 000:533.765 JLINK_ReadReg(R0)
T2180 000:533.780 - 0.015ms returns 0x00000000
T2180 000:534.005 JLINK_HasError()
T2180 000:534.025 JLINK_WriteReg(R0, 0x08000000)
T2180 000:534.040 - 0.015ms returns 0
T2180 000:534.054 JLINK_WriteReg(R1, 0x00004000)
T2180 000:534.068 - 0.014ms returns 0
T2180 000:534.083 JLINK_WriteReg(R2, 0x000000FF)
T2180 000:534.097 - 0.014ms returns 0
T2180 000:534.111 JLINK_WriteReg(R3, 0x00000000)
T2180 000:534.125 - 0.013ms returns 0
T2180 000:534.140 JLINK_WriteReg(R4, 0x00000000)
T2180 000:534.154 - 0.014ms returns 0
T2180 000:534.168 JLINK_WriteReg(R5, 0x00000000)
T2180 000:534.182 - 0.013ms returns 0
T2180 000:534.196 JLINK_WriteReg(R6, 0x00000000)
T2180 000:534.210 - 0.014ms returns 0
T2180 000:534.225 JLINK_WriteReg(R7, 0x00000000)
T2180 000:534.240 - 0.015ms returns 0
T2180 000:534.255 JLINK_WriteReg(R8, 0x00000000)
T2180 000:534.271 - 0.016ms returns 0
T2180 000:534.290 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:534.309 - 0.019ms returns 0
T2180 000:534.332 JLINK_WriteReg(R10, 0x00000000)
T2180 000:534.349 - 0.017ms returns 0
T2180 000:534.368 JLINK_WriteReg(R11, 0x00000000)
T2180 000:534.387 - 0.018ms returns 0
T2180 000:534.405 JLINK_WriteReg(R12, 0x00000000)
T2180 000:534.424 - 0.019ms returns 0
T2180 000:534.443 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:534.462 - 0.019ms returns 0
T2180 000:534.480 JLINK_WriteReg(R14, 0x20000001)
T2180 000:534.494 - 0.014ms returns 0
T2180 000:534.509 JLINK_WriteReg(R15 (PC), 0x20000020)
T2180 000:534.524 - 0.015ms returns 0
T2180 000:534.538 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:534.593 - 0.054ms returns 0
T2180 000:534.610 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:534.624 - 0.014ms returns 0
T2180 000:534.639 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:534.653 - 0.014ms returns 0
T2180 000:534.668 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:534.681 - 0.013ms returns 0
T2180 000:534.696 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:534.711 - 0.015ms returns 0x00000002
T2180 000:534.725 JLINK_Go()
T2180 000:534.746   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:537.708 - 2.982ms
T2180 000:537.728 JLINK_IsHalted()
T2180 000:538.028 - 0.299ms returns FALSE
T2180 000:538.045 JLINK_HasError()
T2180 000:548.561 JLINK_IsHalted()
T2180 000:548.886 - 0.324ms returns FALSE
T2180 000:548.903 JLINK_HasError()
T2180 000:550.165 JLINK_IsHalted()
T2180 000:550.438 - 0.273ms returns FALSE
T2180 000:550.456 JLINK_HasError()
T2180 000:552.978 JLINK_IsHalted()
T2180 000:555.542   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:555.838 - 2.859ms returns TRUE
T2180 000:555.855 JLINK_ReadReg(R15 (PC))
T2180 000:555.871 - 0.015ms returns 0x20000000
T2180 000:555.886 JLINK_ClrBPEx(BPHandle = 0x00000002)
T2180 000:555.900 - 0.014ms returns 0x00
T2180 000:555.915 JLINK_ReadReg(R0)
T2180 000:555.929 - 0.014ms returns 0x00000000
T2180 000:556.275 JLINK_HasError()
T2180 000:556.299 JLINK_WriteReg(R0, 0x08004000)
T2180 000:556.315 - 0.015ms returns 0
T2180 000:556.330 JLINK_WriteReg(R1, 0x00004000)
T2180 000:556.345 - 0.015ms returns 0
T2180 000:556.359 JLINK_WriteReg(R2, 0x000000FF)
T2180 000:556.374 - 0.014ms returns 0
T2180 000:556.388 JLINK_WriteReg(R3, 0x00000000)
T2180 000:556.403 - 0.014ms returns 0
T2180 000:556.417 JLINK_WriteReg(R4, 0x00000000)
T2180 000:556.432 - 0.014ms returns 0
T2180 000:556.446 JLINK_WriteReg(R5, 0x00000000)
T2180 000:556.460 - 0.014ms returns 0
T2180 000:556.475 JLINK_WriteReg(R6, 0x00000000)
T2180 000:556.489 - 0.014ms returns 0
T2180 000:556.504 JLINK_WriteReg(R7, 0x00000000)
T2180 000:556.518 - 0.014ms returns 0
T2180 000:556.533 JLINK_WriteReg(R8, 0x00000000)
T2180 000:556.547 - 0.014ms returns 0
T2180 000:556.562 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:556.576 - 0.014ms returns 0
T2180 000:556.590 JLINK_WriteReg(R10, 0x00000000)
T2180 000:556.605 - 0.014ms returns 0
T2180 000:556.619 JLINK_WriteReg(R11, 0x00000000)
T2180 000:556.633 - 0.014ms returns 0
T2180 000:556.647 JLINK_WriteReg(R12, 0x00000000)
T2180 000:556.662 - 0.014ms returns 0
T2180 000:556.676 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:556.691 - 0.015ms returns 0
T2180 000:556.706 JLINK_WriteReg(R14, 0x20000001)
T2180 000:556.720 - 0.014ms returns 0
T2180 000:556.735 JLINK_WriteReg(R15 (PC), 0x20000020)
T2180 000:556.749 - 0.014ms returns 0
T2180 000:556.764 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:556.778 - 0.014ms returns 0
T2180 000:556.792 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:556.807 - 0.014ms returns 0
T2180 000:556.821 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:556.835 - 0.014ms returns 0
T2180 000:556.852 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:556.865 - 0.013ms returns 0
T2180 000:556.880 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:556.895 - 0.014ms returns 0x00000003
T2180 000:556.909 JLINK_Go()
T2180 000:556.931   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:559.816 - 2.906ms
T2180 000:559.837 JLINK_IsHalted()
T2180 000:560.082 - 0.244ms returns FALSE
T2180 000:560.100 JLINK_HasError()
T2180 000:562.119 JLINK_IsHalted()
T2180 000:562.425 - 0.307ms returns FALSE
T2180 000:562.441 JLINK_HasError()
T2180 000:564.476 JLINK_IsHalted()
T2180 000:564.802 - 0.327ms returns FALSE
T2180 000:564.830 JLINK_HasError()
T2180 000:568.434 JLINK_IsHalted()
T2180 000:568.769 - 0.338ms returns FALSE
T2180 000:568.785 JLINK_HasError()
T2180 000:570.787 JLINK_IsHalted()
T2180 000:571.051 - 0.263ms returns FALSE
T2180 000:571.067 JLINK_HasError()
T2180 000:573.051 JLINK_IsHalted()
T2180 000:573.414 - 0.362ms returns FALSE
T2180 000:573.436 JLINK_HasError()
T2180 000:575.101 JLINK_IsHalted()
T2180 000:577.774   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:578.063 - 2.961ms returns TRUE
T2180 000:578.081 JLINK_ReadReg(R15 (PC))
T2180 000:578.097 - 0.015ms returns 0x20000000
T2180 000:578.111 JLINK_ClrBPEx(BPHandle = 0x00000003)
T2180 000:578.126 - 0.014ms returns 0x00
T2180 000:578.141 JLINK_ReadReg(R0)
T2180 000:578.156 - 0.014ms returns 0x00000000
T2180 000:578.419 JLINK_HasError()
T2180 000:578.441 JLINK_WriteReg(R0, 0x00000001)
T2180 000:578.457 - 0.015ms returns 0
T2180 000:578.471 JLINK_WriteReg(R1, 0x00004000)
T2180 000:578.486 - 0.015ms returns 0
T2180 000:578.501 JLINK_WriteReg(R2, 0x000000FF)
T2180 000:578.515 - 0.014ms returns 0
T2180 000:578.529 JLINK_WriteReg(R3, 0x00000000)
T2180 000:578.544 - 0.014ms returns 0
T2180 000:578.558 JLINK_WriteReg(R4, 0x00000000)
T2180 000:578.573 - 0.014ms returns 0
T2180 000:578.587 JLINK_WriteReg(R5, 0x00000000)
T2180 000:578.601 - 0.014ms returns 0
T2180 000:578.616 JLINK_WriteReg(R6, 0x00000000)
T2180 000:578.630 - 0.014ms returns 0
T2180 000:578.645 JLINK_WriteReg(R7, 0x00000000)
T2180 000:578.659 - 0.013ms returns 0
T2180 000:578.673 JLINK_WriteReg(R8, 0x00000000)
T2180 000:578.687 - 0.014ms returns 0
T2180 000:578.702 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:578.716 - 0.013ms returns 0
T2180 000:578.730 JLINK_WriteReg(R10, 0x00000000)
T2180 000:578.744 - 0.013ms returns 0
T2180 000:578.758 JLINK_WriteReg(R11, 0x00000000)
T2180 000:578.772 - 0.013ms returns 0
T2180 000:578.787 JLINK_WriteReg(R12, 0x00000000)
T2180 000:578.801 - 0.013ms returns 0
T2180 000:578.815 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:578.829 - 0.014ms returns 0
T2180 000:578.844 JLINK_WriteReg(R14, 0x20000001)
T2180 000:578.858 - 0.013ms returns 0
T2180 000:578.872 JLINK_WriteReg(R15 (PC), 0x20000082)
T2180 000:578.886 - 0.014ms returns 0
T2180 000:578.901 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:578.915 - 0.014ms returns 0
T2180 000:578.930 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:578.944 - 0.013ms returns 0
T2180 000:578.958 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:578.972 - 0.014ms returns 0
T2180 000:578.987 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:579.000 - 0.013ms returns 0
T2180 000:579.015 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:579.030 - 0.014ms returns 0x00000004
T2180 000:579.044 JLINK_Go()
T2180 000:579.067   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:581.914 - 2.869ms
T2180 000:581.950 JLINK_IsHalted()
T2180 000:584.435   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:584.711 - 2.760ms returns TRUE
T2180 000:584.731 JLINK_ReadReg(R15 (PC))
T2180 000:584.747 - 0.015ms returns 0x20000000
T2180 000:584.762 JLINK_ClrBPEx(BPHandle = 0x00000004)
T2180 000:584.776 - 0.014ms returns 0x00
T2180 000:584.792 JLINK_ReadReg(R0)
T2180 000:584.806 - 0.014ms returns 0x00000000
T2180 000:639.013 JLINK_WriteMem(0x20000000, 0x180 Bytes, ...)
T2180 000:639.045   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T2180 000:639.079   CPU_WriteMem(384 bytes @ 0x20000000)
T2180 000:641.487 - 2.474ms returns 0x180
T2180 000:641.543 JLINK_HasError()
T2180 000:641.564 JLINK_WriteReg(R0, 0x08000000)
T2180 000:641.585 - 0.021ms returns 0
T2180 000:641.603 JLINK_WriteReg(R1, 0x017D7840)
T2180 000:641.618 - 0.014ms returns 0
T2180 000:641.633 JLINK_WriteReg(R2, 0x00000002)
T2180 000:641.648 - 0.015ms returns 0
T2180 000:641.663 JLINK_WriteReg(R3, 0x00000000)
T2180 000:641.677 - 0.014ms returns 0
T2180 000:641.693 JLINK_WriteReg(R4, 0x00000000)
T2180 000:641.710 - 0.016ms returns 0
T2180 000:641.724 JLINK_WriteReg(R5, 0x00000000)
T2180 000:641.739 - 0.014ms returns 0
T2180 000:641.753 JLINK_WriteReg(R6, 0x00000000)
T2180 000:641.767 - 0.013ms returns 0
T2180 000:641.782 JLINK_WriteReg(R7, 0x00000000)
T2180 000:641.796 - 0.014ms returns 0
T2180 000:641.810 JLINK_WriteReg(R8, 0x00000000)
T2180 000:641.825 - 0.014ms returns 0
T2180 000:641.840 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:641.854 - 0.014ms returns 0
T2180 000:641.868 JLINK_WriteReg(R10, 0x00000000)
T2180 000:641.882 - 0.014ms returns 0
T2180 000:641.903 JLINK_WriteReg(R11, 0x00000000)
T2180 000:641.916 - 0.013ms returns 0
T2180 000:641.931 JLINK_WriteReg(R12, 0x00000000)
T2180 000:641.945 - 0.014ms returns 0
T2180 000:641.959 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:641.974 - 0.014ms returns 0
T2180 000:641.989 JLINK_WriteReg(R14, 0x20000001)
T2180 000:642.003 - 0.014ms returns 0
T2180 000:642.017 JLINK_WriteReg(R15 (PC), 0x20000054)
T2180 000:642.032 - 0.014ms returns 0
T2180 000:642.046 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:642.060 - 0.014ms returns 0
T2180 000:642.075 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:642.089 - 0.014ms returns 0
T2180 000:642.103 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:642.117 - 0.014ms returns 0
T2180 000:642.132 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:642.146 - 0.014ms returns 0
T2180 000:642.161 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:642.180   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:642.488 - 0.327ms returns 0x00000005
T2180 000:642.505 JLINK_Go()
T2180 000:642.521   CPU_WriteMem(2 bytes @ 0x20000000)
T2180 000:642.830   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:645.769 - 3.264ms
T2180 000:645.786 JLINK_IsHalted()
T2180 000:648.331   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:648.614 - 2.828ms returns TRUE
T2180 000:648.631 JLINK_ReadReg(R15 (PC))
T2180 000:648.645 - 0.014ms returns 0x20000000
T2180 000:648.660 JLINK_ClrBPEx(BPHandle = 0x00000005)
T2180 000:648.674 - 0.014ms returns 0x00
T2180 000:648.689 JLINK_ReadReg(R0)
T2180 000:648.703 - 0.014ms returns 0x00000000
T2180 000:648.919 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:648.938   Data:  D8 0B 00 20 99 01 00 08 F1 26 00 08 CD 22 00 08 ...
T2180 000:648.966   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:652.507 - 3.584ms returns 0x280
T2180 000:652.524 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:652.538   Data:  03 46 11 F8 01 2B 00 F8 01 2B 00 2A F9 D1 18 46 ...
T2180 000:652.562   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:654.947 - 2.422ms returns 0x180
T2180 000:654.973 JLINK_HasError()
T2180 000:654.994 JLINK_WriteReg(R0, 0x08000000)
T2180 000:655.011 - 0.021ms returns 0
T2180 000:655.025 JLINK_WriteReg(R1, 0x00000400)
T2180 000:655.040 - 0.014ms returns 0
T2180 000:655.054 JLINK_WriteReg(R2, 0x20000180)
T2180 000:655.068 - 0.014ms returns 0
T2180 000:655.082 JLINK_WriteReg(R3, 0x00000000)
T2180 000:655.096 - 0.014ms returns 0
T2180 000:655.110 JLINK_WriteReg(R4, 0x00000000)
T2180 000:655.124 - 0.014ms returns 0
T2180 000:655.139 JLINK_WriteReg(R5, 0x00000000)
T2180 000:655.153 - 0.014ms returns 0
T2180 000:655.167 JLINK_WriteReg(R6, 0x00000000)
T2180 000:655.182 - 0.014ms returns 0
T2180 000:655.196 JLINK_WriteReg(R7, 0x00000000)
T2180 000:655.210 - 0.013ms returns 0
T2180 000:655.225 JLINK_WriteReg(R8, 0x00000000)
T2180 000:655.239 - 0.014ms returns 0
T2180 000:655.253 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:655.267 - 0.013ms returns 0
T2180 000:655.282 JLINK_WriteReg(R10, 0x00000000)
T2180 000:655.296 - 0.014ms returns 0
T2180 000:655.310 JLINK_WriteReg(R11, 0x00000000)
T2180 000:655.324 - 0.014ms returns 0
T2180 000:655.338 JLINK_WriteReg(R12, 0x00000000)
T2180 000:655.353 - 0.014ms returns 0
T2180 000:655.368 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:655.383 - 0.014ms returns 0
T2180 000:655.397 JLINK_WriteReg(R14, 0x20000001)
T2180 000:655.411 - 0.014ms returns 0
T2180 000:655.426 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:655.440 - 0.014ms returns 0
T2180 000:655.454 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:655.468 - 0.013ms returns 0
T2180 000:655.482 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:655.497 - 0.014ms returns 0
T2180 000:655.511 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:655.525 - 0.013ms returns 0
T2180 000:655.540 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:655.555 - 0.014ms returns 0
T2180 000:655.569 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:655.584 - 0.015ms returns 0x00000006
T2180 000:655.599 JLINK_Go()
T2180 000:655.620   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:658.682 - 3.083ms
T2180 000:658.717 JLINK_IsHalted()
T2180 000:659.001 - 0.283ms returns FALSE
T2180 000:659.017 JLINK_HasError()
T2180 000:662.679 JLINK_IsHalted()
T2180 000:662.966 - 0.290ms returns FALSE
T2180 000:662.986 JLINK_HasError()
T2180 000:665.009 JLINK_IsHalted()
T2180 000:667.555   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:667.847 - 2.840ms returns TRUE
T2180 000:667.864 JLINK_ReadReg(R15 (PC))
T2180 000:667.879 - 0.015ms returns 0x20000000
T2180 000:667.894 JLINK_ClrBPEx(BPHandle = 0x00000006)
T2180 000:667.909 - 0.014ms returns 0x00
T2180 000:667.923 JLINK_ReadReg(R0)
T2180 000:667.938 - 0.014ms returns 0x00000000
T2180 000:668.233 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:668.253   Data:  41 F5 80 11 49 08 4F EA 30 00 00 19 51 41 32 46 ...
T2180 000:668.279   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:671.758 - 3.524ms returns 0x280
T2180 000:671.797 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:671.817   Data:  01 03 94 E8 07 00 98 47 10 34 AC 42 F6 D3 FF F7 ...
T2180 000:671.854   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:674.146 - 2.348ms returns 0x180
T2180 000:674.175 JLINK_HasError()
T2180 000:674.190 JLINK_WriteReg(R0, 0x08000400)
T2180 000:674.206 - 0.015ms returns 0
T2180 000:674.221 JLINK_WriteReg(R1, 0x00000400)
T2180 000:674.235 - 0.014ms returns 0
T2180 000:674.249 JLINK_WriteReg(R2, 0x20000180)
T2180 000:674.263 - 0.014ms returns 0
T2180 000:674.278 JLINK_WriteReg(R3, 0x00000000)
T2180 000:674.293 - 0.015ms returns 0
T2180 000:674.308 JLINK_WriteReg(R4, 0x00000000)
T2180 000:674.322 - 0.014ms returns 0
T2180 000:674.351 JLINK_WriteReg(R5, 0x00000000)
T2180 000:674.365 - 0.013ms returns 0
T2180 000:674.379 JLINK_WriteReg(R6, 0x00000000)
T2180 000:674.394 - 0.014ms returns 0
T2180 000:674.408 JLINK_WriteReg(R7, 0x00000000)
T2180 000:674.422 - 0.013ms returns 0
T2180 000:674.437 JLINK_WriteReg(R8, 0x00000000)
T2180 000:674.451 - 0.014ms returns 0
T2180 000:674.465 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:674.479 - 0.013ms returns 0
T2180 000:674.493 JLINK_WriteReg(R10, 0x00000000)
T2180 000:674.507 - 0.014ms returns 0
T2180 000:674.522 JLINK_WriteReg(R11, 0x00000000)
T2180 000:674.536 - 0.014ms returns 0
T2180 000:674.550 JLINK_WriteReg(R12, 0x00000000)
T2180 000:674.564 - 0.014ms returns 0
T2180 000:674.579 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:674.593 - 0.014ms returns 0
T2180 000:674.608 JLINK_WriteReg(R14, 0x20000001)
T2180 000:674.622 - 0.014ms returns 0
T2180 000:674.636 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:674.650 - 0.014ms returns 0
T2180 000:674.664 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:674.679 - 0.014ms returns 0
T2180 000:674.693 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:674.707 - 0.014ms returns 0
T2180 000:674.722 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:674.736 - 0.014ms returns 0
T2180 000:674.750 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:674.764 - 0.014ms returns 0
T2180 000:674.779 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:674.795 - 0.015ms returns 0x00000007
T2180 000:674.809 JLINK_Go()
T2180 000:674.829   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:677.774 - 2.964ms
T2180 000:677.794 JLINK_IsHalted()
T2180 000:678.060 - 0.266ms returns FALSE
T2180 000:678.076 JLINK_HasError()
T2180 000:681.123 JLINK_IsHalted()
T2180 000:681.416 - 0.292ms returns FALSE
T2180 000:681.432 JLINK_HasError()
T2180 000:683.437 JLINK_IsHalted()
T2180 000:686.010   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:686.288 - 2.851ms returns TRUE
T2180 000:686.305 JLINK_ReadReg(R15 (PC))
T2180 000:686.320 - 0.014ms returns 0x20000000
T2180 000:686.334 JLINK_ClrBPEx(BPHandle = 0x00000007)
T2180 000:686.348 - 0.013ms returns 0x00
T2180 000:686.362 JLINK_ReadReg(R0)
T2180 000:686.375 - 0.013ms returns 0x00000000
T2180 000:686.669 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:686.686   Data:  02 91 01 F0 47 FE 01 98 02 99 01 F0 43 FE 01 98 ...
T2180 000:686.711   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:690.315 - 3.646ms returns 0x280
T2180 000:690.347 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:690.362   Data:  43 F6 14 41 C4 F2 01 01 08 60 BD F8 06 00 FF F7 ...
T2180 000:690.387   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:692.704 - 2.355ms returns 0x180
T2180 000:692.722 JLINK_HasError()
T2180 000:692.737 JLINK_WriteReg(R0, 0x08000800)
T2180 000:692.752 - 0.015ms returns 0
T2180 000:692.768 JLINK_WriteReg(R1, 0x00000400)
T2180 000:692.782 - 0.014ms returns 0
T2180 000:692.797 JLINK_WriteReg(R2, 0x20000180)
T2180 000:692.811 - 0.014ms returns 0
T2180 000:692.826 JLINK_WriteReg(R3, 0x00000000)
T2180 000:692.840 - 0.013ms returns 0
T2180 000:692.855 JLINK_WriteReg(R4, 0x00000000)
T2180 000:692.869 - 0.014ms returns 0
T2180 000:692.884 JLINK_WriteReg(R5, 0x00000000)
T2180 000:692.898 - 0.014ms returns 0
T2180 000:692.914 JLINK_WriteReg(R6, 0x00000000)
T2180 000:692.928 - 0.014ms returns 0
T2180 000:692.943 JLINK_WriteReg(R7, 0x00000000)
T2180 000:692.957 - 0.013ms returns 0
T2180 000:692.972 JLINK_WriteReg(R8, 0x00000000)
T2180 000:692.986 - 0.014ms returns 0
T2180 000:693.001 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:693.015 - 0.014ms returns 0
T2180 000:693.030 JLINK_WriteReg(R10, 0x00000000)
T2180 000:693.045 - 0.014ms returns 0
T2180 000:693.060 JLINK_WriteReg(R11, 0x00000000)
T2180 000:693.074 - 0.014ms returns 0
T2180 000:693.089 JLINK_WriteReg(R12, 0x00000000)
T2180 000:693.103 - 0.014ms returns 0
T2180 000:693.119 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:693.133 - 0.014ms returns 0
T2180 000:693.148 JLINK_WriteReg(R14, 0x20000001)
T2180 000:693.163 - 0.014ms returns 0
T2180 000:693.178 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:693.192 - 0.013ms returns 0
T2180 000:693.207 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:693.221 - 0.014ms returns 0
T2180 000:693.236 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:693.250 - 0.014ms returns 0
T2180 000:693.266 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:693.280 - 0.014ms returns 0
T2180 000:693.295 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:693.309 - 0.014ms returns 0
T2180 000:693.325 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:693.340 - 0.015ms returns 0x00000008
T2180 000:693.355 JLINK_Go()
T2180 000:693.374   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:696.212 - 2.857ms
T2180 000:696.261 JLINK_IsHalted()
T2180 000:696.599 - 0.338ms returns FALSE
T2180 000:696.618 JLINK_HasError()
T2180 000:698.653 JLINK_IsHalted()
T2180 000:698.967 - 0.313ms returns FALSE
T2180 000:698.983 JLINK_HasError()
T2180 000:700.994 JLINK_IsHalted()
T2180 000:703.551   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:703.911 - 2.918ms returns TRUE
T2180 000:703.928 JLINK_ReadReg(R15 (PC))
T2180 000:703.943 - 0.014ms returns 0x20000000
T2180 000:703.958 JLINK_ClrBPEx(BPHandle = 0x00000008)
T2180 000:703.972 - 0.014ms returns 0x00
T2180 000:703.987 JLINK_ReadReg(R0)
T2180 000:704.001 - 0.014ms returns 0x00000000
T2180 000:704.298 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:704.316   Data:  00 FA 01 F1 09 98 08 43 09 90 09 98 0D 99 08 60 ...
T2180 000:704.341   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:707.903 - 3.602ms returns 0x280
T2180 000:707.920 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:707.934   Data:  05 E0 BD F8 02 00 00 04 01 99 88 61 FF E7 02 B0 ...
T2180 000:707.956   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:710.400 - 2.476ms returns 0x180
T2180 000:710.416 JLINK_HasError()
T2180 000:710.431 JLINK_WriteReg(R0, 0x08000C00)
T2180 000:710.446 - 0.014ms returns 0
T2180 000:710.460 JLINK_WriteReg(R1, 0x00000400)
T2180 000:710.476 - 0.015ms returns 0
T2180 000:710.490 JLINK_WriteReg(R2, 0x20000180)
T2180 000:710.506 - 0.015ms returns 0
T2180 000:710.520 JLINK_WriteReg(R3, 0x00000000)
T2180 000:710.534 - 0.014ms returns 0
T2180 000:710.549 JLINK_WriteReg(R4, 0x00000000)
T2180 000:710.563 - 0.014ms returns 0
T2180 000:710.578 JLINK_WriteReg(R5, 0x00000000)
T2180 000:710.592 - 0.014ms returns 0
T2180 000:710.606 JLINK_WriteReg(R6, 0x00000000)
T2180 000:710.623 - 0.017ms returns 0
T2180 000:710.640 JLINK_WriteReg(R7, 0x00000000)
T2180 000:710.654 - 0.014ms returns 0
T2180 000:710.671 JLINK_WriteReg(R8, 0x00000000)
T2180 000:710.685 - 0.016ms returns 0
T2180 000:710.700 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:710.714 - 0.014ms returns 0
T2180 000:710.728 JLINK_WriteReg(R10, 0x00000000)
T2180 000:710.742 - 0.014ms returns 0
T2180 000:710.757 JLINK_WriteReg(R11, 0x00000000)
T2180 000:710.771 - 0.014ms returns 0
T2180 000:710.785 JLINK_WriteReg(R12, 0x00000000)
T2180 000:710.799 - 0.014ms returns 0
T2180 000:710.814 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:710.828 - 0.014ms returns 0
T2180 000:710.843 JLINK_WriteReg(R14, 0x20000001)
T2180 000:710.857 - 0.014ms returns 0
T2180 000:710.871 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:710.885 - 0.014ms returns 0
T2180 000:710.900 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:710.914 - 0.014ms returns 0
T2180 000:710.928 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:710.942 - 0.014ms returns 0
T2180 000:710.957 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:710.971 - 0.014ms returns 0
T2180 000:710.986 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:711.000 - 0.014ms returns 0
T2180 000:711.015 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:711.029 - 0.014ms returns 0x00000009
T2180 000:711.044 JLINK_Go()
T2180 000:711.062   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:714.270 - 3.226ms
T2180 000:714.285 JLINK_IsHalted()
T2180 000:714.663 - 0.377ms returns FALSE
T2180 000:714.678 JLINK_HasError()
T2180 000:716.687 JLINK_IsHalted()
T2180 000:716.949 - 0.263ms returns FALSE
T2180 000:716.964 JLINK_HasError()
T2180 000:719.179 JLINK_IsHalted()
T2180 000:721.891   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:722.213 - 3.034ms returns TRUE
T2180 000:722.233 JLINK_ReadReg(R15 (PC))
T2180 000:722.248 - 0.015ms returns 0x20000000
T2180 000:722.264 JLINK_ClrBPEx(BPHandle = 0x00000009)
T2180 000:722.278 - 0.014ms returns 0x00
T2180 000:722.294 JLINK_ReadReg(R0)
T2180 000:722.308 - 0.014ms returns 0x00000000
T2180 000:722.633 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:722.651   Data:  02 F0 26 FB 02 B0 80 BD 80 B5 84 B0 02 90 01 91 ...
T2180 000:722.676   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:726.149 - 3.513ms returns 0x280
T2180 000:726.167 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:726.181   Data:  40 B1 FF E7 01 98 04 28 0A D0 FF E7 01 98 08 28 ...
T2180 000:726.203   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:728.575 - 2.405ms returns 0x180
T2180 000:728.592 JLINK_HasError()
T2180 000:728.607 JLINK_WriteReg(R0, 0x08001000)
T2180 000:728.622 - 0.014ms returns 0
T2180 000:728.637 JLINK_WriteReg(R1, 0x00000400)
T2180 000:728.651 - 0.014ms returns 0
T2180 000:728.665 JLINK_WriteReg(R2, 0x20000180)
T2180 000:728.679 - 0.014ms returns 0
T2180 000:728.694 JLINK_WriteReg(R3, 0x00000000)
T2180 000:728.708 - 0.013ms returns 0
T2180 000:728.722 JLINK_WriteReg(R4, 0x00000000)
T2180 000:728.736 - 0.014ms returns 0
T2180 000:728.751 JLINK_WriteReg(R5, 0x00000000)
T2180 000:728.765 - 0.014ms returns 0
T2180 000:728.779 JLINK_WriteReg(R6, 0x00000000)
T2180 000:728.793 - 0.014ms returns 0
T2180 000:728.808 JLINK_WriteReg(R7, 0x00000000)
T2180 000:728.822 - 0.013ms returns 0
T2180 000:728.836 JLINK_WriteReg(R8, 0x00000000)
T2180 000:728.851 - 0.014ms returns 0
T2180 000:728.865 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:728.879 - 0.013ms returns 0
T2180 000:728.894 JLINK_WriteReg(R10, 0x00000000)
T2180 000:728.907 - 0.013ms returns 0
T2180 000:728.922 JLINK_WriteReg(R11, 0x00000000)
T2180 000:728.936 - 0.014ms returns 0
T2180 000:728.951 JLINK_WriteReg(R12, 0x00000000)
T2180 000:728.965 - 0.014ms returns 0
T2180 000:728.979 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:728.994 - 0.014ms returns 0
T2180 000:729.008 JLINK_WriteReg(R14, 0x20000001)
T2180 000:729.022 - 0.014ms returns 0
T2180 000:729.037 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:729.051 - 0.014ms returns 0
T2180 000:729.065 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:729.082 - 0.016ms returns 0
T2180 000:729.099 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:729.113 - 0.014ms returns 0
T2180 000:729.127 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:729.141 - 0.014ms returns 0
T2180 000:729.156 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:729.170 - 0.014ms returns 0
T2180 000:729.184 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:729.199 - 0.014ms returns 0x0000000A
T2180 000:729.213 JLINK_Go()
T2180 000:729.232   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:732.149 - 2.935ms
T2180 000:732.165 JLINK_IsHalted()
T2180 000:732.432 - 0.266ms returns FALSE
T2180 000:732.447 JLINK_HasError()
T2180 000:735.593 JLINK_IsHalted()
T2180 000:735.907 - 0.314ms returns FALSE
T2180 000:735.924 JLINK_HasError()
T2180 000:737.745 JLINK_IsHalted()
T2180 000:740.392   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:740.679 - 2.934ms returns TRUE
T2180 000:740.695 JLINK_ReadReg(R15 (PC))
T2180 000:740.711 - 0.015ms returns 0x20000000
T2180 000:740.726 JLINK_ClrBPEx(BPHandle = 0x0000000A)
T2180 000:740.741 - 0.015ms returns 0x00
T2180 000:740.755 JLINK_ReadReg(R0)
T2180 000:740.769 - 0.014ms returns 0x00000000
T2180 000:741.062 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:741.080   Data:  FF E7 FF F7 47 FD 03 99 40 1A 65 28 04 D3 FF E7 ...
T2180 000:741.104   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:744.607 - 3.544ms returns 0x280
T2180 000:744.632 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:744.646   Data:  0B 00 FF E7 47 F2 00 00 C4 F2 00 00 01 68 41 F4 ...
T2180 000:744.672   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:747.071 - 2.439ms returns 0x180
T2180 000:747.100 JLINK_HasError()
T2180 000:747.117 JLINK_WriteReg(R0, 0x08001400)
T2180 000:747.134 - 0.016ms returns 0
T2180 000:747.149 JLINK_WriteReg(R1, 0x00000400)
T2180 000:747.180 - 0.030ms returns 0
T2180 000:747.194 JLINK_WriteReg(R2, 0x20000180)
T2180 000:747.213 - 0.018ms returns 0
T2180 000:747.230 JLINK_WriteReg(R3, 0x00000000)
T2180 000:747.245 - 0.014ms returns 0
T2180 000:747.260 JLINK_WriteReg(R4, 0x00000000)
T2180 000:747.275 - 0.014ms returns 0
T2180 000:747.290 JLINK_WriteReg(R5, 0x00000000)
T2180 000:747.322 - 0.031ms returns 0
T2180 000:747.337 JLINK_WriteReg(R6, 0x00000000)
T2180 000:747.351 - 0.014ms returns 0
T2180 000:747.367 JLINK_WriteReg(R7, 0x00000000)
T2180 000:747.381 - 0.014ms returns 0
T2180 000:747.397 JLINK_WriteReg(R8, 0x00000000)
T2180 000:747.411 - 0.014ms returns 0
T2180 000:747.426 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:747.440 - 0.014ms returns 0
T2180 000:747.455 JLINK_WriteReg(R10, 0x00000000)
T2180 000:747.469 - 0.014ms returns 0
T2180 000:747.484 JLINK_WriteReg(R11, 0x00000000)
T2180 000:747.499 - 0.014ms returns 0
T2180 000:747.514 JLINK_WriteReg(R12, 0x00000000)
T2180 000:747.528 - 0.013ms returns 0
T2180 000:747.543 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:747.558 - 0.014ms returns 0
T2180 000:747.574 JLINK_WriteReg(R14, 0x20000001)
T2180 000:747.588 - 0.014ms returns 0
T2180 000:747.603 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:747.617 - 0.014ms returns 0
T2180 000:747.633 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:747.646 - 0.013ms returns 0
T2180 000:747.662 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:747.676 - 0.014ms returns 0
T2180 000:747.691 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:747.705 - 0.013ms returns 0
T2180 000:747.720 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:747.734 - 0.014ms returns 0
T2180 000:747.750 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:747.765 - 0.015ms returns 0x0000000B
T2180 000:747.780 JLINK_Go()
T2180 000:747.802   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:750.650 - 2.869ms
T2180 000:750.683 JLINK_IsHalted()
T2180 000:751.075 - 0.391ms returns FALSE
T2180 000:751.094 JLINK_HasError()
T2180 000:754.909 JLINK_IsHalted()
T2180 000:757.466   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:757.760 - 2.854ms returns TRUE
T2180 000:757.777 JLINK_ReadReg(R15 (PC))
T2180 000:757.793 - 0.016ms returns 0x20000000
T2180 000:757.811 JLINK_ClrBPEx(BPHandle = 0x0000000B)
T2180 000:757.827 - 0.015ms returns 0x00
T2180 000:757.841 JLINK_ReadReg(R0)
T2180 000:757.856 - 0.014ms returns 0x00000000
T2180 000:758.163 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:758.182   Data:  43 F6 04 01 C4 F2 02 01 08 68 48 F2 00 02 CF F2 ...
T2180 000:758.208   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:761.724 - 3.560ms returns 0x280
T2180 000:761.741 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:761.755   Data:  41 68 00 98 08 43 00 90 00 98 03 99 09 68 88 60 ...
T2180 000:761.777   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:764.038 - 2.297ms returns 0x180
T2180 000:764.061 JLINK_HasError()
T2180 000:764.092 JLINK_WriteReg(R0, 0x08001800)
T2180 000:764.108 - 0.016ms returns 0
T2180 000:764.123 JLINK_WriteReg(R1, 0x00000400)
T2180 000:764.137 - 0.014ms returns 0
T2180 000:764.152 JLINK_WriteReg(R2, 0x20000180)
T2180 000:764.166 - 0.014ms returns 0
T2180 000:764.181 JLINK_WriteReg(R3, 0x00000000)
T2180 000:764.195 - 0.014ms returns 0
T2180 000:764.210 JLINK_WriteReg(R4, 0x00000000)
T2180 000:764.224 - 0.014ms returns 0
T2180 000:764.238 JLINK_WriteReg(R5, 0x00000000)
T2180 000:764.252 - 0.014ms returns 0
T2180 000:764.267 JLINK_WriteReg(R6, 0x00000000)
T2180 000:764.281 - 0.013ms returns 0
T2180 000:764.295 JLINK_WriteReg(R7, 0x00000000)
T2180 000:764.309 - 0.014ms returns 0
T2180 000:764.324 JLINK_WriteReg(R8, 0x00000000)
T2180 000:764.338 - 0.014ms returns 0
T2180 000:764.352 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:764.366 - 0.013ms returns 0
T2180 000:764.381 JLINK_WriteReg(R10, 0x00000000)
T2180 000:764.395 - 0.014ms returns 0
T2180 000:764.409 JLINK_WriteReg(R11, 0x00000000)
T2180 000:764.423 - 0.014ms returns 0
T2180 000:764.438 JLINK_WriteReg(R12, 0x00000000)
T2180 000:764.452 - 0.014ms returns 0
T2180 000:764.466 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:764.481 - 0.014ms returns 0
T2180 000:764.495 JLINK_WriteReg(R14, 0x20000001)
T2180 000:764.509 - 0.014ms returns 0
T2180 000:764.524 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:764.538 - 0.014ms returns 0
T2180 000:764.553 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:764.567 - 0.014ms returns 0
T2180 000:764.582 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:764.596 - 0.014ms returns 0
T2180 000:764.610 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:764.624 - 0.013ms returns 0
T2180 000:764.639 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:764.653 - 0.013ms returns 0
T2180 000:764.667 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:764.682 - 0.014ms returns 0x0000000C
T2180 000:764.696 JLINK_Go()
T2180 000:764.716   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:767.681 - 2.980ms
T2180 000:767.714 JLINK_IsHalted()
T2180 000:767.982 - 0.267ms returns FALSE
T2180 000:767.998 JLINK_HasError()
T2180 000:770.367 JLINK_IsHalted()
T2180 000:770.674 - 0.306ms returns FALSE
T2180 000:770.690 JLINK_HasError()
T2180 000:772.720 JLINK_IsHalted()
T2180 000:775.308   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:775.611 - 2.890ms returns TRUE
T2180 000:775.627 JLINK_ReadReg(R15 (PC))
T2180 000:775.643 - 0.015ms returns 0x20000000
T2180 000:775.657 JLINK_ClrBPEx(BPHandle = 0x0000000C)
T2180 000:775.672 - 0.014ms returns 0x00
T2180 000:775.687 JLINK_ReadReg(R0)
T2180 000:775.701 - 0.014ms returns 0x00000000
T2180 000:775.983 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:776.001   Data:  02 98 FF E7 1D 20 00 90 00 22 11 46 FF F7 DA F9 ...
T2180 000:776.026   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:779.563 - 3.580ms returns 0x280
T2180 000:779.590 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:779.609   Data:  04 99 00 20 81 F8 3C 00 FF E7 9D F8 0B 00 8D F8 ...
T2180 000:779.645   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:781.989 - 2.398ms returns 0x180
T2180 000:782.018 JLINK_HasError()
T2180 000:782.034 JLINK_WriteReg(R0, 0x08001C00)
T2180 000:782.051 - 0.016ms returns 0
T2180 000:782.067 JLINK_WriteReg(R1, 0x00000400)
T2180 000:782.081 - 0.014ms returns 0
T2180 000:782.100 JLINK_WriteReg(R2, 0x20000180)
T2180 000:782.116 - 0.016ms returns 0
T2180 000:782.132 JLINK_WriteReg(R3, 0x00000000)
T2180 000:782.146 - 0.014ms returns 0
T2180 000:782.161 JLINK_WriteReg(R4, 0x00000000)
T2180 000:782.175 - 0.014ms returns 0
T2180 000:782.191 JLINK_WriteReg(R5, 0x00000000)
T2180 000:782.205 - 0.014ms returns 0
T2180 000:782.220 JLINK_WriteReg(R6, 0x00000000)
T2180 000:782.235 - 0.014ms returns 0
T2180 000:782.250 JLINK_WriteReg(R7, 0x00000000)
T2180 000:782.264 - 0.014ms returns 0
T2180 000:782.280 JLINK_WriteReg(R8, 0x00000000)
T2180 000:782.294 - 0.014ms returns 0
T2180 000:782.309 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:782.323 - 0.014ms returns 0
T2180 000:782.338 JLINK_WriteReg(R10, 0x00000000)
T2180 000:782.353 - 0.014ms returns 0
T2180 000:782.368 JLINK_WriteReg(R11, 0x00000000)
T2180 000:782.382 - 0.014ms returns 0
T2180 000:782.398 JLINK_WriteReg(R12, 0x00000000)
T2180 000:782.412 - 0.014ms returns 0
T2180 000:782.427 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:782.442 - 0.014ms returns 0
T2180 000:782.457 JLINK_WriteReg(R14, 0x20000001)
T2180 000:782.471 - 0.014ms returns 0
T2180 000:782.487 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:782.501 - 0.014ms returns 0
T2180 000:782.516 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:782.530 - 0.014ms returns 0
T2180 000:782.546 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:782.560 - 0.014ms returns 0
T2180 000:782.575 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:782.589 - 0.013ms returns 0
T2180 000:782.604 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:782.618 - 0.013ms returns 0
T2180 000:782.634 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:782.649 - 0.015ms returns 0x0000000D
T2180 000:782.664 JLINK_Go()
T2180 000:782.685   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:785.608 - 2.943ms
T2180 000:785.629 JLINK_IsHalted()
T2180 000:785.974 - 0.345ms returns FALSE
T2180 000:785.993 JLINK_HasError()
T2180 000:788.034 JLINK_IsHalted()
T2180 000:788.336 - 0.304ms returns FALSE
T2180 000:788.352 JLINK_HasError()
T2180 000:790.008 JLINK_IsHalted()
T2180 000:792.683   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:792.984 - 2.977ms returns TRUE
T2180 000:793.021 JLINK_ReadReg(R15 (PC))
T2180 000:793.036 - 0.015ms returns 0x20000000
T2180 000:793.051 JLINK_ClrBPEx(BPHandle = 0x0000000D)
T2180 000:793.065 - 0.014ms returns 0x00
T2180 000:793.080 JLINK_ReadReg(R0)
T2180 000:793.095 - 0.014ms returns 0x00000000
T2180 000:793.393 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:793.413   Data:  63 F8 FF E7 03 99 00 20 08 77 FF E7 FF E7 9D F8 ...
T2180 000:793.438   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:797.028 - 3.633ms returns 0x280
T2180 000:797.064 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:797.079   Data:  08 5C 81 F8 85 00 03 99 91 F8 41 00 01 30 00 F0 ...
T2180 000:797.106   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:799.419 - 2.354ms returns 0x180
T2180 000:799.444 JLINK_HasError()
T2180 000:799.461 JLINK_WriteReg(R0, 0x08002000)
T2180 000:799.478 - 0.016ms returns 0
T2180 000:799.494 JLINK_WriteReg(R1, 0x00000400)
T2180 000:799.509 - 0.015ms returns 0
T2180 000:799.524 JLINK_WriteReg(R2, 0x20000180)
T2180 000:799.540 - 0.015ms returns 0
T2180 000:799.555 JLINK_WriteReg(R3, 0x00000000)
T2180 000:799.570 - 0.014ms returns 0
T2180 000:799.586 JLINK_WriteReg(R4, 0x00000000)
T2180 000:799.601 - 0.015ms returns 0
T2180 000:799.616 JLINK_WriteReg(R5, 0x00000000)
T2180 000:799.631 - 0.015ms returns 0
T2180 000:799.647 JLINK_WriteReg(R6, 0x00000000)
T2180 000:799.662 - 0.015ms returns 0
T2180 000:799.678 JLINK_WriteReg(R7, 0x00000000)
T2180 000:799.693 - 0.015ms returns 0
T2180 000:799.708 JLINK_WriteReg(R8, 0x00000000)
T2180 000:799.723 - 0.014ms returns 0
T2180 000:799.739 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:799.754 - 0.014ms returns 0
T2180 000:799.769 JLINK_WriteReg(R10, 0x00000000)
T2180 000:799.784 - 0.015ms returns 0
T2180 000:799.800 JLINK_WriteReg(R11, 0x00000000)
T2180 000:799.815 - 0.015ms returns 0
T2180 000:799.831 JLINK_WriteReg(R12, 0x00000000)
T2180 000:799.851 - 0.019ms returns 0
T2180 000:799.867 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:799.882 - 0.015ms returns 0
T2180 000:799.898 JLINK_WriteReg(R14, 0x20000001)
T2180 000:799.912 - 0.014ms returns 0
T2180 000:799.929 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:799.945 - 0.015ms returns 0
T2180 000:799.961 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:799.976 - 0.015ms returns 0
T2180 000:799.992 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:800.008 - 0.015ms returns 0
T2180 000:800.024 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:800.039 - 0.015ms returns 0
T2180 000:800.055 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:800.071 - 0.015ms returns 0
T2180 000:800.088 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:800.104 - 0.016ms returns 0x0000000E
T2180 000:800.120 JLINK_Go()
T2180 000:800.141   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:803.024 - 2.903ms
T2180 000:803.047 JLINK_IsHalted()
T2180 000:803.311 - 0.263ms returns FALSE
T2180 000:803.326 JLINK_HasError()
T2180 000:807.690 JLINK_IsHalted()
T2180 000:810.238   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:810.602 - 2.911ms returns TRUE
T2180 000:810.637 JLINK_ReadReg(R15 (PC))
T2180 000:810.659 - 0.022ms returns 0x20000000
T2180 000:810.679 JLINK_ClrBPEx(BPHandle = 0x0000000E)
T2180 000:810.698 - 0.019ms returns 0x00
T2180 000:810.718 JLINK_ReadReg(R0)
T2180 000:810.737 - 0.019ms returns 0x00000000
T2180 000:811.098 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:811.119   Data:  0F FC 40 F2 84 20 C2 F2 00 00 FF F7 09 FC 0E B0 ...
T2180 000:811.146   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:814.619 - 3.520ms returns 0x280
T2180 000:814.646 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:814.660   Data:  C2 F2 00 00 40 F2 00 42 C4 F2 00 02 02 60 63 22 ...
T2180 000:814.689   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:817.070 - 2.423ms returns 0x180
T2180 000:817.087 JLINK_HasError()
T2180 000:817.117 JLINK_WriteReg(R0, 0x08002400)
T2180 000:817.132 - 0.015ms returns 0
T2180 000:817.146 JLINK_WriteReg(R1, 0x00000400)
T2180 000:817.160 - 0.013ms returns 0
T2180 000:817.175 JLINK_WriteReg(R2, 0x20000180)
T2180 000:817.189 - 0.014ms returns 0
T2180 000:817.204 JLINK_WriteReg(R3, 0x00000000)
T2180 000:817.218 - 0.014ms returns 0
T2180 000:817.234 JLINK_WriteReg(R4, 0x00000000)
T2180 000:817.248 - 0.013ms returns 0
T2180 000:817.263 JLINK_WriteReg(R5, 0x00000000)
T2180 000:817.277 - 0.013ms returns 0
T2180 000:817.292 JLINK_WriteReg(R6, 0x00000000)
T2180 000:817.307 - 0.014ms returns 0
T2180 000:817.322 JLINK_WriteReg(R7, 0x00000000)
T2180 000:817.336 - 0.014ms returns 0
T2180 000:817.351 JLINK_WriteReg(R8, 0x00000000)
T2180 000:817.365 - 0.014ms returns 0
T2180 000:817.381 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:817.395 - 0.014ms returns 0
T2180 000:817.410 JLINK_WriteReg(R10, 0x00000000)
T2180 000:817.424 - 0.013ms returns 0
T2180 000:817.439 JLINK_WriteReg(R11, 0x00000000)
T2180 000:817.453 - 0.013ms returns 0
T2180 000:817.468 JLINK_WriteReg(R12, 0x00000000)
T2180 000:817.482 - 0.014ms returns 0
T2180 000:817.497 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:817.512 - 0.014ms returns 0
T2180 000:817.527 JLINK_WriteReg(R14, 0x20000001)
T2180 000:817.541 - 0.013ms returns 0
T2180 000:817.556 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:817.570 - 0.014ms returns 0
T2180 000:817.585 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:817.599 - 0.014ms returns 0
T2180 000:817.614 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:817.628 - 0.014ms returns 0
T2180 000:817.644 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:817.657 - 0.014ms returns 0
T2180 000:817.673 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:817.686 - 0.013ms returns 0
T2180 000:817.702 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:817.717 - 0.015ms returns 0x0000000F
T2180 000:817.733 JLINK_Go()
T2180 000:817.752   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:820.602 - 2.869ms
T2180 000:820.625 JLINK_IsHalted()
T2180 000:820.933 - 0.308ms returns FALSE
T2180 000:820.959 JLINK_HasError()
T2180 000:822.965 JLINK_IsHalted()
T2180 000:823.286 - 0.323ms returns FALSE
T2180 000:823.302 JLINK_HasError()
T2180 000:825.325 JLINK_IsHalted()
T2180 000:827.901   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:828.289 - 2.965ms returns TRUE
T2180 000:828.306 JLINK_ReadReg(R15 (PC))
T2180 000:828.322 - 0.015ms returns 0x20000000
T2180 000:828.336 JLINK_ClrBPEx(BPHandle = 0x0000000F)
T2180 000:828.351 - 0.014ms returns 0x00
T2180 000:828.366 JLINK_ReadReg(R0)
T2180 000:828.380 - 0.014ms returns 0x00000000
T2180 000:828.708 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:828.728   Data:  A3 FD 18 B1 FF E7 FE F7 F5 F8 FF E7 0F 20 01 90 ...
T2180 000:828.754   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:832.246 - 3.537ms returns 0x280
T2180 000:832.272 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:832.286   Data:  08 6A 20 F0 01 00 08 62 04 98 80 69 01 90 01 98 ...
T2180 000:832.310   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:834.577 - 2.305ms returns 0x180
T2180 000:834.609 JLINK_HasError()
T2180 000:834.625 JLINK_WriteReg(R0, 0x08002800)
T2180 000:834.641 - 0.016ms returns 0
T2180 000:834.656 JLINK_WriteReg(R1, 0x00000400)
T2180 000:834.670 - 0.014ms returns 0
T2180 000:834.684 JLINK_WriteReg(R2, 0x20000180)
T2180 000:834.699 - 0.014ms returns 0
T2180 000:834.713 JLINK_WriteReg(R3, 0x00000000)
T2180 000:834.727 - 0.014ms returns 0
T2180 000:834.742 JLINK_WriteReg(R4, 0x00000000)
T2180 000:834.756 - 0.014ms returns 0
T2180 000:834.770 JLINK_WriteReg(R5, 0x00000000)
T2180 000:834.784 - 0.014ms returns 0
T2180 000:834.799 JLINK_WriteReg(R6, 0x00000000)
T2180 000:834.813 - 0.014ms returns 0
T2180 000:834.828 JLINK_WriteReg(R7, 0x00000000)
T2180 000:834.842 - 0.014ms returns 0
T2180 000:834.856 JLINK_WriteReg(R8, 0x00000000)
T2180 000:834.870 - 0.014ms returns 0
T2180 000:834.885 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:834.899 - 0.013ms returns 0
T2180 000:834.913 JLINK_WriteReg(R10, 0x00000000)
T2180 000:834.927 - 0.014ms returns 0
T2180 000:834.941 JLINK_WriteReg(R11, 0x00000000)
T2180 000:834.955 - 0.014ms returns 0
T2180 000:834.970 JLINK_WriteReg(R12, 0x00000000)
T2180 000:834.983 - 0.013ms returns 0
T2180 000:834.998 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:835.012 - 0.014ms returns 0
T2180 000:835.027 JLINK_WriteReg(R14, 0x20000001)
T2180 000:835.041 - 0.014ms returns 0
T2180 000:835.056 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:835.070 - 0.014ms returns 0
T2180 000:835.084 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:835.098 - 0.014ms returns 0
T2180 000:835.113 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:835.127 - 0.014ms returns 0
T2180 000:835.141 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:835.155 - 0.013ms returns 0
T2180 000:835.169 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:835.183 - 0.014ms returns 0
T2180 000:835.198 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:835.213 - 0.014ms returns 0x00000010
T2180 000:835.227 JLINK_Go()
T2180 000:835.247   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:838.140 - 2.913ms
T2180 000:838.158 JLINK_IsHalted()
T2180 000:838.432 - 0.273ms returns FALSE
T2180 000:838.448 JLINK_HasError()
T2180 000:841.151 JLINK_IsHalted()
T2180 000:841.485 - 0.335ms returns FALSE
T2180 000:841.508 JLINK_HasError()
T2180 000:842.843 JLINK_IsHalted()
T2180 000:845.477   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:845.748 - 2.904ms returns TRUE
T2180 000:845.767 JLINK_ReadReg(R15 (PC))
T2180 000:845.783 - 0.016ms returns 0x20000000
T2180 000:845.798 JLINK_ClrBPEx(BPHandle = 0x00000010)
T2180 000:845.816 - 0.017ms returns 0x00
T2180 000:845.831 JLINK_ReadReg(R0)
T2180 000:845.863 - 0.032ms returns 0x00000000
T2180 000:846.223 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:846.247   Data:  03 00 9D F8 03 00 08 28 03 DB FF E7 00 20 01 90 ...
T2180 000:846.274   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:849.751 - 3.528ms returns 0x280
T2180 000:849.776 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:849.790   Data:  30 B1 FF E7 9D F8 4E 00 00 21 01 F0 B5 FB FF E7 ...
T2180 000:849.819   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:852.198 - 2.422ms returns 0x180
T2180 000:852.219 JLINK_HasError()
T2180 000:852.235 JLINK_WriteReg(R0, 0x08002C00)
T2180 000:852.250 - 0.015ms returns 0
T2180 000:852.266 JLINK_WriteReg(R1, 0x00000400)
T2180 000:852.280 - 0.014ms returns 0
T2180 000:852.295 JLINK_WriteReg(R2, 0x20000180)
T2180 000:852.309 - 0.014ms returns 0
T2180 000:852.325 JLINK_WriteReg(R3, 0x00000000)
T2180 000:852.339 - 0.014ms returns 0
T2180 000:852.354 JLINK_WriteReg(R4, 0x00000000)
T2180 000:852.368 - 0.014ms returns 0
T2180 000:852.384 JLINK_WriteReg(R5, 0x00000000)
T2180 000:852.398 - 0.014ms returns 0
T2180 000:852.413 JLINK_WriteReg(R6, 0x00000000)
T2180 000:852.427 - 0.014ms returns 0
T2180 000:852.443 JLINK_WriteReg(R7, 0x00000000)
T2180 000:852.457 - 0.014ms returns 0
T2180 000:852.472 JLINK_WriteReg(R8, 0x00000000)
T2180 000:852.486 - 0.014ms returns 0
T2180 000:852.502 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:852.516 - 0.014ms returns 0
T2180 000:852.531 JLINK_WriteReg(R10, 0x00000000)
T2180 000:852.545 - 0.014ms returns 0
T2180 000:852.560 JLINK_WriteReg(R11, 0x00000000)
T2180 000:852.574 - 0.014ms returns 0
T2180 000:852.590 JLINK_WriteReg(R12, 0x00000000)
T2180 000:852.604 - 0.014ms returns 0
T2180 000:852.619 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:852.634 - 0.014ms returns 0
T2180 000:852.649 JLINK_WriteReg(R14, 0x20000001)
T2180 000:852.663 - 0.014ms returns 0
T2180 000:852.679 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:852.693 - 0.014ms returns 0
T2180 000:852.708 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:852.722 - 0.014ms returns 0
T2180 000:852.738 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:852.751 - 0.013ms returns 0
T2180 000:852.767 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:852.781 - 0.014ms returns 0
T2180 000:852.796 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:852.810 - 0.014ms returns 0
T2180 000:852.826 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:852.840 - 0.014ms returns 0x00000011
T2180 000:852.856 JLINK_Go()
T2180 000:852.875   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:855.703 - 2.846ms
T2180 000:855.725 JLINK_IsHalted()
T2180 000:855.991 - 0.265ms returns FALSE
T2180 000:856.008 JLINK_HasError()
T2180 000:857.410 JLINK_IsHalted()
T2180 000:857.689 - 0.279ms returns FALSE
T2180 000:857.706 JLINK_HasError()
T2180 000:859.755 JLINK_IsHalted()
T2180 000:860.025 - 0.270ms returns FALSE
T2180 000:860.048 JLINK_HasError()
T2180 000:862.080 JLINK_IsHalted()
T2180 000:864.676   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:864.962 - 2.884ms returns TRUE
T2180 000:864.981 JLINK_ReadReg(R15 (PC))
T2180 000:864.996 - 0.015ms returns 0x20000000
T2180 000:865.010 JLINK_ClrBPEx(BPHandle = 0x00000011)
T2180 000:865.025 - 0.014ms returns 0x00
T2180 000:865.040 JLINK_ReadReg(R0)
T2180 000:865.054 - 0.014ms returns 0x00000000
T2180 000:865.467 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:865.486   Data:  00 EB C1 00 40 78 A0 B1 FF E7 9D F8 4B 10 68 46 ...
T2180 000:865.511   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:869.001 - 3.531ms returns 0x280
T2180 000:869.019 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:869.033   Data:  52 00 9D F8 55 00 8D F8 53 00 9D F8 56 10 6A 46 ...
T2180 000:869.055   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:871.365 - 2.345ms returns 0x180
T2180 000:871.390 JLINK_HasError()
T2180 000:871.406 JLINK_WriteReg(R0, 0x08003000)
T2180 000:871.422 - 0.016ms returns 0
T2180 000:871.437 JLINK_WriteReg(R1, 0x00000400)
T2180 000:871.452 - 0.014ms returns 0
T2180 000:871.466 JLINK_WriteReg(R2, 0x20000180)
T2180 000:871.480 - 0.014ms returns 0
T2180 000:871.495 JLINK_WriteReg(R3, 0x00000000)
T2180 000:871.510 - 0.014ms returns 0
T2180 000:871.524 JLINK_WriteReg(R4, 0x00000000)
T2180 000:871.539 - 0.014ms returns 0
T2180 000:871.554 JLINK_WriteReg(R5, 0x00000000)
T2180 000:871.568 - 0.014ms returns 0
T2180 000:871.582 JLINK_WriteReg(R6, 0x00000000)
T2180 000:871.652 - 0.069ms returns 0
T2180 000:871.671 JLINK_WriteReg(R7, 0x00000000)
T2180 000:871.684 - 0.013ms returns 0
T2180 000:871.699 JLINK_WriteReg(R8, 0x00000000)
T2180 000:871.713 - 0.014ms returns 0
T2180 000:871.728 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:871.749 - 0.021ms returns 0
T2180 000:871.764 JLINK_WriteReg(R10, 0x00000000)
T2180 000:871.779 - 0.014ms returns 0
T2180 000:871.794 JLINK_WriteReg(R11, 0x00000000)
T2180 000:871.808 - 0.014ms returns 0
T2180 000:871.823 JLINK_WriteReg(R12, 0x00000000)
T2180 000:871.837 - 0.013ms returns 0
T2180 000:871.852 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:871.871 - 0.019ms returns 0
T2180 000:871.885 JLINK_WriteReg(R14, 0x20000001)
T2180 000:871.899 - 0.014ms returns 0
T2180 000:871.914 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:871.928 - 0.014ms returns 0
T2180 000:871.942 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:871.957 - 0.014ms returns 0
T2180 000:871.972 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:871.987 - 0.015ms returns 0
T2180 000:872.002 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:872.016 - 0.014ms returns 0
T2180 000:872.030 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:872.045 - 0.014ms returns 0
T2180 000:872.060 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:872.075 - 0.015ms returns 0x00000012
T2180 000:872.090 JLINK_Go()
T2180 000:872.110   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:875.149 - 3.059ms
T2180 000:875.167 JLINK_IsHalted()
T2180 000:875.444 - 0.275ms returns FALSE
T2180 000:875.460 JLINK_HasError()
T2180 000:877.500 JLINK_IsHalted()
T2180 000:877.783 - 0.283ms returns FALSE
T2180 000:877.801 JLINK_HasError()
T2180 000:879.775 JLINK_IsHalted()
T2180 000:882.314   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:882.611 - 2.835ms returns TRUE
T2180 000:882.629 JLINK_ReadReg(R15 (PC))
T2180 000:882.645 - 0.015ms returns 0x20000000
T2180 000:882.661 JLINK_ClrBPEx(BPHandle = 0x00000012)
T2180 000:882.676 - 0.014ms returns 0x00
T2180 000:882.691 JLINK_ReadReg(R0)
T2180 000:882.706 - 0.014ms returns 0x00000000
T2180 000:883.023 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:883.042   Data:  05 90 05 98 44 F6 D3 51 C1 F2 62 01 A0 FB 01 10 ...
T2180 000:883.067   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:886.541 - 3.518ms returns 0x280
T2180 000:886.566 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:886.580   Data:  10 43 01 90 01 98 08 60 03 B0 70 47 81 B0 8D F8 ...
T2180 000:886.604   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:888.925 - 2.356ms returns 0x180
T2180 000:888.945 JLINK_HasError()
T2180 000:888.961 JLINK_WriteReg(R0, 0x08003400)
T2180 000:888.976 - 0.015ms returns 0
T2180 000:888.990 JLINK_WriteReg(R1, 0x00000400)
T2180 000:889.004 - 0.014ms returns 0
T2180 000:889.019 JLINK_WriteReg(R2, 0x20000180)
T2180 000:889.033 - 0.014ms returns 0
T2180 000:889.048 JLINK_WriteReg(R3, 0x00000000)
T2180 000:889.061 - 0.013ms returns 0
T2180 000:889.076 JLINK_WriteReg(R4, 0x00000000)
T2180 000:889.090 - 0.014ms returns 0
T2180 000:889.104 JLINK_WriteReg(R5, 0x00000000)
T2180 000:889.118 - 0.014ms returns 0
T2180 000:889.133 JLINK_WriteReg(R6, 0x00000000)
T2180 000:889.147 - 0.014ms returns 0
T2180 000:889.161 JLINK_WriteReg(R7, 0x00000000)
T2180 000:889.175 - 0.014ms returns 0
T2180 000:889.190 JLINK_WriteReg(R8, 0x00000000)
T2180 000:889.204 - 0.014ms returns 0
T2180 000:889.219 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:889.233 - 0.014ms returns 0
T2180 000:889.247 JLINK_WriteReg(R10, 0x00000000)
T2180 000:889.261 - 0.014ms returns 0
T2180 000:889.276 JLINK_WriteReg(R11, 0x00000000)
T2180 000:889.290 - 0.014ms returns 0
T2180 000:889.304 JLINK_WriteReg(R12, 0x00000000)
T2180 000:889.318 - 0.014ms returns 0
T2180 000:889.333 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:889.347 - 0.014ms returns 0
T2180 000:889.362 JLINK_WriteReg(R14, 0x20000001)
T2180 000:889.376 - 0.014ms returns 0
T2180 000:889.391 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:889.406 - 0.015ms returns 0
T2180 000:889.421 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:889.438 - 0.017ms returns 0
T2180 000:889.456 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:889.470 - 0.013ms returns 0
T2180 000:889.484 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:889.498 - 0.014ms returns 0
T2180 000:889.513 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:889.527 - 0.014ms returns 0
T2180 000:889.542 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:889.557 - 0.015ms returns 0x00000013
T2180 000:889.571 JLINK_Go()
T2180 000:889.590   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:892.596 - 3.024ms
T2180 000:892.613 JLINK_IsHalted()
T2180 000:892.907 - 0.293ms returns FALSE
T2180 000:892.923 JLINK_HasError()
T2180 000:895.958 JLINK_IsHalted()
T2180 000:896.261 - 0.302ms returns FALSE
T2180 000:896.278 JLINK_HasError()
T2180 000:898.310 JLINK_IsHalted()
T2180 000:900.830   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:901.213 - 2.904ms returns TRUE
T2180 000:901.230 JLINK_ReadReg(R15 (PC))
T2180 000:901.245 - 0.014ms returns 0x20000000
T2180 000:901.260 JLINK_ClrBPEx(BPHandle = 0x00000013)
T2180 000:901.274 - 0.014ms returns 0x00
T2180 000:901.288 JLINK_ReadReg(R0)
T2180 000:901.303 - 0.014ms returns 0x00000000
T2180 000:901.594 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:901.612   Data:  B8 71 C2 F2 00 01 08 60 FF E7 0A 20 FD F7 FA F8 ...
T2180 000:901.637   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:905.078 - 3.483ms returns 0x280
T2180 000:905.104 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:905.121   Data:  00 00 45 F2 6A 32 C0 F6 00 02 00 F0 FD FD FF E7 ...
T2180 000:905.146   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:907.465 - 2.361ms returns 0x180
T2180 000:907.482 JLINK_HasError()
T2180 000:907.497 JLINK_WriteReg(R0, 0x08003800)
T2180 000:907.512 - 0.015ms returns 0
T2180 000:907.527 JLINK_WriteReg(R1, 0x00000400)
T2180 000:907.541 - 0.014ms returns 0
T2180 000:907.556 JLINK_WriteReg(R2, 0x20000180)
T2180 000:907.570 - 0.014ms returns 0
T2180 000:907.584 JLINK_WriteReg(R3, 0x00000000)
T2180 000:907.598 - 0.014ms returns 0
T2180 000:907.614 JLINK_WriteReg(R4, 0x00000000)
T2180 000:907.628 - 0.014ms returns 0
T2180 000:907.668 JLINK_WriteReg(R5, 0x00000000)
T2180 000:907.683 - 0.015ms returns 0
T2180 000:907.697 JLINK_WriteReg(R6, 0x00000000)
T2180 000:907.712 - 0.014ms returns 0
T2180 000:907.726 JLINK_WriteReg(R7, 0x00000000)
T2180 000:907.740 - 0.014ms returns 0
T2180 000:907.755 JLINK_WriteReg(R8, 0x00000000)
T2180 000:907.769 - 0.014ms returns 0
T2180 000:907.783 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:907.797 - 0.014ms returns 0
T2180 000:907.812 JLINK_WriteReg(R10, 0x00000000)
T2180 000:907.826 - 0.014ms returns 0
T2180 000:907.841 JLINK_WriteReg(R11, 0x00000000)
T2180 000:907.855 - 0.014ms returns 0
T2180 000:907.869 JLINK_WriteReg(R12, 0x00000000)
T2180 000:907.883 - 0.014ms returns 0
T2180 000:907.898 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:907.912 - 0.014ms returns 0
T2180 000:907.927 JLINK_WriteReg(R14, 0x20000001)
T2180 000:907.941 - 0.014ms returns 0
T2180 000:907.955 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:907.969 - 0.014ms returns 0
T2180 000:907.984 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:907.998 - 0.014ms returns 0
T2180 000:908.013 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:908.026 - 0.013ms returns 0
T2180 000:908.041 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:908.055 - 0.013ms returns 0
T2180 000:908.069 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:908.083 - 0.014ms returns 0
T2180 000:908.098 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:908.113 - 0.015ms returns 0x00000014
T2180 000:908.128 JLINK_Go()
T2180 000:908.147   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:911.126 - 2.998ms
T2180 000:911.149 JLINK_IsHalted()
T2180 000:911.511 - 0.361ms returns FALSE
T2180 000:911.528 JLINK_HasError()
T2180 000:914.559 JLINK_IsHalted()
T2180 000:914.847 - 0.290ms returns FALSE
T2180 000:914.863 JLINK_HasError()
T2180 000:916.537 JLINK_IsHalted()
T2180 000:919.091   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:919.461 - 2.923ms returns TRUE
T2180 000:919.482 JLINK_ReadReg(R15 (PC))
T2180 000:919.497 - 0.014ms returns 0x20000000
T2180 000:919.511 JLINK_ClrBPEx(BPHandle = 0x00000014)
T2180 000:919.524 - 0.013ms returns 0x00
T2180 000:919.539 JLINK_ReadReg(R0)
T2180 000:919.552 - 0.013ms returns 0x00000000
T2180 000:919.850 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:919.868   Data:  FF F7 C0 FB 8D F8 06 00 FF E7 40 F2 C0 71 C2 F2 ...
T2180 000:919.894   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:923.478 - 3.628ms returns 0x280
T2180 000:923.495 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:923.510   Data:  C1 FD 00 20 8D F8 1F 00 FF E7 9D F8 1F 00 07 28 ...
T2180 000:923.532   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:925.933 - 2.437ms returns 0x180
T2180 000:925.948 JLINK_HasError()
T2180 000:925.963 JLINK_WriteReg(R0, 0x08003C00)
T2180 000:925.977 - 0.014ms returns 0
T2180 000:925.991 JLINK_WriteReg(R1, 0x00000400)
T2180 000:926.004 - 0.013ms returns 0
T2180 000:926.018 JLINK_WriteReg(R2, 0x20000180)
T2180 000:926.032 - 0.013ms returns 0
T2180 000:926.046 JLINK_WriteReg(R3, 0x00000000)
T2180 000:926.059 - 0.013ms returns 0
T2180 000:926.073 JLINK_WriteReg(R4, 0x00000000)
T2180 000:926.086 - 0.013ms returns 0
T2180 000:926.100 JLINK_WriteReg(R5, 0x00000000)
T2180 000:926.113 - 0.013ms returns 0
T2180 000:926.127 JLINK_WriteReg(R6, 0x00000000)
T2180 000:926.140 - 0.013ms returns 0
T2180 000:926.154 JLINK_WriteReg(R7, 0x00000000)
T2180 000:926.167 - 0.013ms returns 0
T2180 000:926.181 JLINK_WriteReg(R8, 0x00000000)
T2180 000:926.195 - 0.013ms returns 0
T2180 000:926.208 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:926.222 - 0.013ms returns 0
T2180 000:926.236 JLINK_WriteReg(R10, 0x00000000)
T2180 000:926.249 - 0.013ms returns 0
T2180 000:926.263 JLINK_WriteReg(R11, 0x00000000)
T2180 000:926.276 - 0.013ms returns 0
T2180 000:926.290 JLINK_WriteReg(R12, 0x00000000)
T2180 000:926.303 - 0.013ms returns 0
T2180 000:926.317 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:926.331 - 0.013ms returns 0
T2180 000:926.344 JLINK_WriteReg(R14, 0x20000001)
T2180 000:926.358 - 0.013ms returns 0
T2180 000:926.371 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:926.385 - 0.013ms returns 0
T2180 000:926.399 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:926.412 - 0.013ms returns 0
T2180 000:926.426 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:926.440 - 0.013ms returns 0
T2180 000:926.454 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:926.467 - 0.013ms returns 0
T2180 000:926.481 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:926.494 - 0.013ms returns 0
T2180 000:926.508 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:926.522 - 0.014ms returns 0x00000015
T2180 000:926.536 JLINK_Go()
T2180 000:926.554   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:929.703 - 3.166ms
T2180 000:929.720 JLINK_IsHalted()
T2180 000:930.041 - 0.321ms returns FALSE
T2180 000:930.057 JLINK_HasError()
T2180 000:932.092 JLINK_IsHalted()
T2180 000:932.460 - 0.370ms returns FALSE
T2180 000:932.475 JLINK_HasError()
T2180 000:933.882 JLINK_IsHalted()
T2180 000:936.453   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:936.714 - 2.831ms returns TRUE
T2180 000:936.730 JLINK_ReadReg(R15 (PC))
T2180 000:936.745 - 0.014ms returns 0x20000000
T2180 000:936.759 JLINK_ClrBPEx(BPHandle = 0x00000015)
T2180 000:936.772 - 0.013ms returns 0x00
T2180 000:936.786 JLINK_ReadReg(R0)
T2180 000:936.800 - 0.013ms returns 0x00000000
T2180 000:937.060 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:937.077   Data:  FF E7 40 F2 C0 71 C2 F2 00 01 08 68 01 30 08 60 ...
T2180 000:937.101   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:940.605 - 3.544ms returns 0x280
T2180 000:940.621 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:940.635   Data:  40 F2 C0 71 C2 F2 00 01 08 68 01 30 08 60 9D F8 ...
T2180 000:940.656   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:943.018 - 2.396ms returns 0x180
T2180 000:943.034 JLINK_HasError()
T2180 000:943.048 JLINK_WriteReg(R0, 0x08004000)
T2180 000:943.062 - 0.014ms returns 0
T2180 000:943.081 JLINK_WriteReg(R1, 0x00000400)
T2180 000:943.094 - 0.013ms returns 0
T2180 000:943.108 JLINK_WriteReg(R2, 0x20000180)
T2180 000:943.122 - 0.013ms returns 0
T2180 000:943.136 JLINK_WriteReg(R3, 0x00000000)
T2180 000:943.149 - 0.013ms returns 0
T2180 000:943.163 JLINK_WriteReg(R4, 0x00000000)
T2180 000:943.176 - 0.013ms returns 0
T2180 000:943.190 JLINK_WriteReg(R5, 0x00000000)
T2180 000:943.203 - 0.013ms returns 0
T2180 000:943.217 JLINK_WriteReg(R6, 0x00000000)
T2180 000:943.231 - 0.013ms returns 0
T2180 000:943.245 JLINK_WriteReg(R7, 0x00000000)
T2180 000:943.258 - 0.013ms returns 0
T2180 000:943.272 JLINK_WriteReg(R8, 0x00000000)
T2180 000:943.286 - 0.013ms returns 0
T2180 000:943.300 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:943.313 - 0.013ms returns 0
T2180 000:943.327 JLINK_WriteReg(R10, 0x00000000)
T2180 000:943.341 - 0.013ms returns 0
T2180 000:943.355 JLINK_WriteReg(R11, 0x00000000)
T2180 000:943.368 - 0.013ms returns 0
T2180 000:943.382 JLINK_WriteReg(R12, 0x00000000)
T2180 000:943.396 - 0.013ms returns 0
T2180 000:943.410 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:943.423 - 0.013ms returns 0
T2180 000:943.437 JLINK_WriteReg(R14, 0x20000001)
T2180 000:943.451 - 0.013ms returns 0
T2180 000:943.465 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:943.478 - 0.013ms returns 0
T2180 000:943.492 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:943.505 - 0.013ms returns 0
T2180 000:943.519 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:943.532 - 0.013ms returns 0
T2180 000:943.546 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:943.560 - 0.013ms returns 0
T2180 000:943.573 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:943.587 - 0.013ms returns 0
T2180 000:943.601 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:943.615 - 0.014ms returns 0x00000016
T2180 000:943.629 JLINK_Go()
T2180 000:943.648   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:946.849 - 3.220ms
T2180 000:946.865 JLINK_IsHalted()
T2180 000:947.235 - 0.369ms returns FALSE
T2180 000:947.250 JLINK_HasError()
T2180 000:949.488 JLINK_IsHalted()
T2180 000:949.784 - 0.296ms returns FALSE
T2180 000:949.801 JLINK_HasError()
T2180 000:952.470 JLINK_IsHalted()
T2180 000:955.030   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:955.416 - 2.948ms returns TRUE
T2180 000:955.433 JLINK_ReadReg(R15 (PC))
T2180 000:955.448 - 0.015ms returns 0x20000000
T2180 000:955.463 JLINK_ClrBPEx(BPHandle = 0x00000016)
T2180 000:955.477 - 0.014ms returns 0x00
T2180 000:955.492 JLINK_ReadReg(R0)
T2180 000:955.506 - 0.014ms returns 0x00000000
T2180 000:955.801 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:955.819   Data:  81 60 01 68 45 F2 01 10 C0 F6 00 00 45 F2 A4 22 ...
T2180 000:955.844   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:959.321 - 3.520ms returns 0x280
T2180 000:959.347 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:959.362   Data:  FF E7 06 B0 80 BD 00 00 0F B4 05 4B 10 B5 03 A9 ...
T2180 000:959.386   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:961.613 - 2.266ms returns 0x180
T2180 000:961.632 JLINK_HasError()
T2180 000:961.648 JLINK_WriteReg(R0, 0x08004400)
T2180 000:961.662 - 0.014ms returns 0
T2180 000:961.678 JLINK_WriteReg(R1, 0x00000400)
T2180 000:961.692 - 0.014ms returns 0
T2180 000:961.707 JLINK_WriteReg(R2, 0x20000180)
T2180 000:961.722 - 0.014ms returns 0
T2180 000:961.737 JLINK_WriteReg(R3, 0x00000000)
T2180 000:961.751 - 0.014ms returns 0
T2180 000:961.767 JLINK_WriteReg(R4, 0x00000000)
T2180 000:961.781 - 0.014ms returns 0
T2180 000:961.796 JLINK_WriteReg(R5, 0x00000000)
T2180 000:961.810 - 0.013ms returns 0
T2180 000:961.825 JLINK_WriteReg(R6, 0x00000000)
T2180 000:961.839 - 0.014ms returns 0
T2180 000:961.854 JLINK_WriteReg(R7, 0x00000000)
T2180 000:961.868 - 0.013ms returns 0
T2180 000:961.884 JLINK_WriteReg(R8, 0x00000000)
T2180 000:961.898 - 0.014ms returns 0
T2180 000:961.913 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:961.927 - 0.014ms returns 0
T2180 000:961.943 JLINK_WriteReg(R10, 0x00000000)
T2180 000:961.957 - 0.014ms returns 0
T2180 000:961.976 JLINK_WriteReg(R11, 0x00000000)
T2180 000:961.993 - 0.016ms returns 0
T2180 000:962.008 JLINK_WriteReg(R12, 0x00000000)
T2180 000:962.022 - 0.014ms returns 0
T2180 000:962.037 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:962.052 - 0.014ms returns 0
T2180 000:962.067 JLINK_WriteReg(R14, 0x20000001)
T2180 000:962.081 - 0.014ms returns 0
T2180 000:962.096 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:962.110 - 0.014ms returns 0
T2180 000:962.125 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:962.139 - 0.014ms returns 0
T2180 000:962.155 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:962.169 - 0.014ms returns 0
T2180 000:962.184 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:962.198 - 0.014ms returns 0
T2180 000:962.213 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:962.227 - 0.014ms returns 0
T2180 000:962.243 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:962.258 - 0.014ms returns 0x00000017
T2180 000:962.273 JLINK_Go()
T2180 000:962.292   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:965.148 - 2.873ms
T2180 000:965.192 JLINK_IsHalted()
T2180 000:965.445 - 0.253ms returns FALSE
T2180 000:965.462 JLINK_HasError()
T2180 000:967.686 JLINK_IsHalted()
T2180 000:967.938 - 0.251ms returns FALSE
T2180 000:967.954 JLINK_HasError()
T2180 000:969.991 JLINK_IsHalted()
T2180 000:972.489   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:972.751 - 2.761ms returns TRUE
T2180 000:972.768 JLINK_ReadReg(R15 (PC))
T2180 000:972.784 - 0.016ms returns 0x20000000
T2180 000:972.799 JLINK_ClrBPEx(BPHandle = 0x00000017)
T2180 000:972.813 - 0.014ms returns 0x00
T2180 000:972.828 JLINK_ReadReg(R0)
T2180 000:972.842 - 0.014ms returns 0x00000000
T2180 000:973.121 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:973.139   Data:  FB F7 DC FC 03 9B 30 32 1A 55 64 1E 50 EA 01 02 ...
T2180 000:973.164   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:976.635 - 3.514ms returns 0x280
T2180 000:976.654 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:976.668   Data:  42 E0 0A 22 00 92 C4 F3 02 52 4F F0 00 0A 02 2A ...
T2180 000:976.690   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:979.087 - 2.432ms returns 0x180
T2180 000:979.129 JLINK_HasError()
T2180 000:979.151 JLINK_WriteReg(R0, 0x08004800)
T2180 000:979.172 - 0.020ms returns 0
T2180 000:979.193 JLINK_WriteReg(R1, 0x00000400)
T2180 000:979.208 - 0.014ms returns 0
T2180 000:979.222 JLINK_WriteReg(R2, 0x20000180)
T2180 000:979.242 - 0.019ms returns 0
T2180 000:979.256 JLINK_WriteReg(R3, 0x00000000)
T2180 000:979.270 - 0.014ms returns 0
T2180 000:979.285 JLINK_WriteReg(R4, 0x00000000)
T2180 000:979.314 - 0.029ms returns 0
T2180 000:979.329 JLINK_WriteReg(R5, 0x00000000)
T2180 000:979.343 - 0.014ms returns 0
T2180 000:979.364 JLINK_WriteReg(R6, 0x00000000)
T2180 000:979.383 - 0.019ms returns 0
T2180 000:979.418 JLINK_WriteReg(R7, 0x00000000)
T2180 000:979.440 - 0.022ms returns 0
T2180 000:979.472 JLINK_WriteReg(R8, 0x00000000)
T2180 000:979.490 - 0.018ms returns 0
T2180 000:979.517 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:979.549 - 0.032ms returns 0
T2180 000:979.568 JLINK_WriteReg(R10, 0x00000000)
T2180 000:979.586 - 0.018ms returns 0
T2180 000:979.605 JLINK_WriteReg(R11, 0x00000000)
T2180 000:979.624 - 0.019ms returns 0
T2180 000:979.645 JLINK_WriteReg(R12, 0x00000000)
T2180 000:979.664 - 0.019ms returns 0
T2180 000:979.685 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:979.704 - 0.019ms returns 0
T2180 000:979.723 JLINK_WriteReg(R14, 0x20000001)
T2180 000:979.742 - 0.018ms returns 0
T2180 000:979.762 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:979.781 - 0.019ms returns 0
T2180 000:979.800 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:979.819 - 0.019ms returns 0
T2180 000:979.838 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:979.856 - 0.018ms returns 0
T2180 000:979.875 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:979.894 - 0.019ms returns 0
T2180 000:979.913 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:979.934 - 0.020ms returns 0
T2180 000:979.955 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:979.998 - 0.043ms returns 0x00000018
T2180 000:980.023 JLINK_Go()
T2180 000:980.051   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 000:983.121 - 3.097ms
T2180 000:983.141 JLINK_IsHalted()
T2180 000:983.487 - 0.346ms returns FALSE
T2180 000:983.503 JLINK_HasError()
T2180 000:985.340 JLINK_IsHalted()
T2180 000:985.613 - 0.273ms returns FALSE
T2180 000:985.630 JLINK_HasError()
T2180 000:987.364 JLINK_IsHalted()
T2180 000:990.034   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 000:990.414 - 3.051ms returns TRUE
T2180 000:990.433 JLINK_ReadReg(R15 (PC))
T2180 000:990.462 - 0.029ms returns 0x20000000
T2180 000:990.477 JLINK_ClrBPEx(BPHandle = 0x00000018)
T2180 000:990.492 - 0.014ms returns 0x00
T2180 000:990.506 JLINK_ReadReg(R0)
T2180 000:990.521 - 0.014ms returns 0x00000000
T2180 000:990.853 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 000:990.871   Data:  00 90 05 98 41 44 40 1A 05 90 E0 03 06 D4 5B 46 ...
T2180 000:990.896   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 000:994.488 - 3.635ms returns 0x280
T2180 000:994.521 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 000:994.544   Data:  0E 98 08 70 30 78 00 F0 20 00 40 F0 45 00 07 F8 ...
T2180 000:994.590   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 000:996.922 - 2.401ms returns 0x180
T2180 000:996.943 JLINK_HasError()
T2180 000:996.958 JLINK_WriteReg(R0, 0x08004C00)
T2180 000:996.973 - 0.015ms returns 0
T2180 000:996.987 JLINK_WriteReg(R1, 0x00000400)
T2180 000:997.000 - 0.013ms returns 0
T2180 000:997.014 JLINK_WriteReg(R2, 0x20000180)
T2180 000:997.028 - 0.013ms returns 0
T2180 000:997.041 JLINK_WriteReg(R3, 0x00000000)
T2180 000:997.055 - 0.013ms returns 0
T2180 000:997.069 JLINK_WriteReg(R4, 0x00000000)
T2180 000:997.083 - 0.013ms returns 0
T2180 000:997.096 JLINK_WriteReg(R5, 0x00000000)
T2180 000:997.110 - 0.013ms returns 0
T2180 000:997.124 JLINK_WriteReg(R6, 0x00000000)
T2180 000:997.137 - 0.013ms returns 0
T2180 000:997.151 JLINK_WriteReg(R7, 0x00000000)
T2180 000:997.164 - 0.013ms returns 0
T2180 000:997.179 JLINK_WriteReg(R8, 0x00000000)
T2180 000:997.192 - 0.013ms returns 0
T2180 000:997.206 JLINK_WriteReg(R9, 0x2000017C)
T2180 000:997.219 - 0.013ms returns 0
T2180 000:997.233 JLINK_WriteReg(R10, 0x00000000)
T2180 000:997.247 - 0.013ms returns 0
T2180 000:997.261 JLINK_WriteReg(R11, 0x00000000)
T2180 000:997.274 - 0.013ms returns 0
T2180 000:997.288 JLINK_WriteReg(R12, 0x00000000)
T2180 000:997.302 - 0.013ms returns 0
T2180 000:997.316 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 000:997.330 - 0.014ms returns 0
T2180 000:997.344 JLINK_WriteReg(R14, 0x20000001)
T2180 000:997.357 - 0.013ms returns 0
T2180 000:997.371 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 000:997.385 - 0.013ms returns 0
T2180 000:997.399 JLINK_WriteReg(XPSR, 0x01000000)
T2180 000:997.412 - 0.013ms returns 0
T2180 000:997.426 JLINK_WriteReg(MSP, 0x20001000)
T2180 000:997.440 - 0.013ms returns 0
T2180 000:997.454 JLINK_WriteReg(PSP, 0x20001000)
T2180 000:997.467 - 0.013ms returns 0
T2180 000:997.481 JLINK_WriteReg(CFBP, 0x00000000)
T2180 000:997.495 - 0.013ms returns 0
T2180 000:997.509 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 000:997.524 - 0.015ms returns 0x00000019
T2180 000:997.538 JLINK_Go()
T2180 000:997.557   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 001:000.649 - 3.110ms
T2180 001:000.673 JLINK_IsHalted()
T2180 001:000.943 - 0.269ms returns FALSE
T2180 001:000.959 JLINK_HasError()
T2180 001:003.396 JLINK_IsHalted()
T2180 001:003.657 - 0.263ms returns FALSE
T2180 001:003.674 JLINK_HasError()
T2180 001:005.696 JLINK_IsHalted()
T2180 001:008.269   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 001:008.537 - 2.840ms returns TRUE
T2180 001:008.554 JLINK_ReadReg(R15 (PC))
T2180 001:008.569 - 0.015ms returns 0x20000000
T2180 001:008.583 JLINK_ClrBPEx(BPHandle = 0x00000019)
T2180 001:008.597 - 0.014ms returns 0x00
T2180 001:008.612 JLINK_ReadReg(R0)
T2180 001:008.626 - 0.014ms returns 0x00000000
T2180 001:009.039 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 001:009.058   Data:  74 73 20 50 41 53 53 45 44 21 00 43 6F 6E 74 72 ...
T2180 001:009.087   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 001:012.603 - 3.563ms returns 0x280
T2180 001:012.632 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 001:012.647   Data:  3D 20 42 4F 41 52 44 5F 53 54 41 54 55 53 5F 54 ...
T2180 001:012.673   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 001:014.953 - 2.320ms returns 0x180
T2180 001:014.971 JLINK_HasError()
T2180 001:014.986 JLINK_WriteReg(R0, 0x08005000)
T2180 001:015.002 - 0.015ms returns 0
T2180 001:015.016 JLINK_WriteReg(R1, 0x00000400)
T2180 001:015.031 - 0.014ms returns 0
T2180 001:015.045 JLINK_WriteReg(R2, 0x20000180)
T2180 001:015.059 - 0.013ms returns 0
T2180 001:015.073 JLINK_WriteReg(R3, 0x00000000)
T2180 001:015.088 - 0.014ms returns 0
T2180 001:015.102 JLINK_WriteReg(R4, 0x00000000)
T2180 001:015.117 - 0.015ms returns 0
T2180 001:015.132 JLINK_WriteReg(R5, 0x00000000)
T2180 001:015.146 - 0.014ms returns 0
T2180 001:015.160 JLINK_WriteReg(R6, 0x00000000)
T2180 001:015.174 - 0.013ms returns 0
T2180 001:015.188 JLINK_WriteReg(R7, 0x00000000)
T2180 001:015.202 - 0.014ms returns 0
T2180 001:015.217 JLINK_WriteReg(R8, 0x00000000)
T2180 001:015.231 - 0.014ms returns 0
T2180 001:015.245 JLINK_WriteReg(R9, 0x2000017C)
T2180 001:015.259 - 0.014ms returns 0
T2180 001:015.274 JLINK_WriteReg(R10, 0x00000000)
T2180 001:015.288 - 0.014ms returns 0
T2180 001:015.304 JLINK_WriteReg(R11, 0x00000000)
T2180 001:015.320 - 0.015ms returns 0
T2180 001:015.334 JLINK_WriteReg(R12, 0x00000000)
T2180 001:015.349 - 0.014ms returns 0
T2180 001:015.363 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 001:015.379 - 0.016ms returns 0
T2180 001:015.395 JLINK_WriteReg(R14, 0x20000001)
T2180 001:015.416 - 0.020ms returns 0
T2180 001:015.448 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 001:015.463 - 0.014ms returns 0
T2180 001:015.486 JLINK_WriteReg(XPSR, 0x01000000)
T2180 001:015.505 - 0.019ms returns 0
T2180 001:015.523 JLINK_WriteReg(MSP, 0x20001000)
T2180 001:015.537 - 0.013ms returns 0
T2180 001:015.553 JLINK_WriteReg(PSP, 0x20001000)
T2180 001:015.567 - 0.014ms returns 0
T2180 001:015.582 JLINK_WriteReg(CFBP, 0x00000000)
T2180 001:015.596 - 0.014ms returns 0
T2180 001:015.612 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 001:015.627 - 0.015ms returns 0x0000001A
T2180 001:015.642 JLINK_Go()
T2180 001:015.662   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 001:018.616 - 2.972ms
T2180 001:018.652 JLINK_IsHalted()
T2180 001:018.907 - 0.255ms returns FALSE
T2180 001:018.925 JLINK_HasError()
T2180 001:021.507 JLINK_IsHalted()
T2180 001:021.770 - 0.264ms returns FALSE
T2180 001:021.787 JLINK_HasError()
T2180 001:023.498 JLINK_IsHalted()
T2180 001:025.989   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 001:026.321 - 2.823ms returns TRUE
T2180 001:026.378 JLINK_ReadReg(R15 (PC))
T2180 001:026.418 - 0.039ms returns 0x20000000
T2180 001:026.456 JLINK_ClrBPEx(BPHandle = 0x0000001A)
T2180 001:026.492 - 0.036ms returns 0x00
T2180 001:026.529 JLINK_ReadReg(R0)
T2180 001:026.565 - 0.036ms returns 0x00000000
T2180 001:027.137 JLINK_WriteMem(0x20000180, 0x280 Bytes, ...)
T2180 001:027.156   Data:  75 65 00 72 65 73 65 74 5F 72 65 73 75 6C 74 20 ...
T2180 001:027.183   CPU_WriteMem(640 bytes @ 0x20000180)
T2180 001:030.677 - 3.540ms returns 0x280
T2180 001:030.694 JLINK_WriteMem(0x20000400, 0x180 Bytes, ...)
T2180 001:030.708   Data:  FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF ...
T2180 001:030.731   CPU_WriteMem(384 bytes @ 0x20000400)
T2180 001:033.066 - 2.372ms returns 0x180
T2180 001:033.083 JLINK_HasError()
T2180 001:033.098 JLINK_WriteReg(R0, 0x08005400)
T2180 001:033.113 - 0.014ms returns 0
T2180 001:033.128 JLINK_WriteReg(R1, 0x00000130)
T2180 001:033.142 - 0.014ms returns 0
T2180 001:033.157 JLINK_WriteReg(R2, 0x20000180)
T2180 001:033.171 - 0.014ms returns 0
T2180 001:033.186 JLINK_WriteReg(R3, 0x00000000)
T2180 001:033.200 - 0.014ms returns 0
T2180 001:033.214 JLINK_WriteReg(R4, 0x00000000)
T2180 001:033.228 - 0.014ms returns 0
T2180 001:033.243 JLINK_WriteReg(R5, 0x00000000)
T2180 001:033.262 - 0.019ms returns 0
T2180 001:033.277 JLINK_WriteReg(R6, 0x00000000)
T2180 001:033.291 - 0.014ms returns 0
T2180 001:033.305 JLINK_WriteReg(R7, 0x00000000)
T2180 001:033.319 - 0.014ms returns 0
T2180 001:033.334 JLINK_WriteReg(R8, 0x00000000)
T2180 001:033.348 - 0.014ms returns 0
T2180 001:033.362 JLINK_WriteReg(R9, 0x2000017C)
T2180 001:033.376 - 0.013ms returns 0
T2180 001:033.390 JLINK_WriteReg(R10, 0x00000000)
T2180 001:033.404 - 0.014ms returns 0
T2180 001:033.419 JLINK_WriteReg(R11, 0x00000000)
T2180 001:033.433 - 0.014ms returns 0
T2180 001:033.448 JLINK_WriteReg(R12, 0x00000000)
T2180 001:033.462 - 0.014ms returns 0
T2180 001:033.477 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 001:033.491 - 0.014ms returns 0
T2180 001:033.505 JLINK_WriteReg(R14, 0x20000001)
T2180 001:033.519 - 0.014ms returns 0
T2180 001:033.534 JLINK_WriteReg(R15 (PC), 0x20000108)
T2180 001:033.548 - 0.013ms returns 0
T2180 001:033.562 JLINK_WriteReg(XPSR, 0x01000000)
T2180 001:033.576 - 0.014ms returns 0
T2180 001:033.590 JLINK_WriteReg(MSP, 0x20001000)
T2180 001:033.604 - 0.014ms returns 0
T2180 001:033.619 JLINK_WriteReg(PSP, 0x20001000)
T2180 001:033.633 - 0.013ms returns 0
T2180 001:033.647 JLINK_WriteReg(CFBP, 0x00000000)
T2180 001:033.662 - 0.014ms returns 0
T2180 001:033.677 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 001:033.691 - 0.014ms returns 0x0000001B
T2180 001:033.705 JLINK_Go()
T2180 001:033.725   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 001:036.643 - 2.937ms
T2180 001:036.659 JLINK_IsHalted()
T2180 001:036.931 - 0.271ms returns FALSE
T2180 001:036.947 JLINK_HasError()
T2180 001:038.974 JLINK_IsHalted()
T2180 001:041.463   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 001:041.736 - 2.763ms returns TRUE
T2180 001:041.756 JLINK_ReadReg(R15 (PC))
T2180 001:041.772 - 0.015ms returns 0x20000000
T2180 001:041.786 JLINK_ClrBPEx(BPHandle = 0x0000001B)
T2180 001:041.801 - 0.014ms returns 0x00
T2180 001:041.815 JLINK_ReadReg(R0)
T2180 001:041.830 - 0.014ms returns 0x00000000
T2180 001:041.845 JLINK_HasError()
T2180 001:041.860 JLINK_WriteReg(R0, 0x00000002)
T2180 001:041.874 - 0.014ms returns 0
T2180 001:041.889 JLINK_WriteReg(R1, 0x00000130)
T2180 001:041.904 - 0.015ms returns 0
T2180 001:041.920 JLINK_WriteReg(R2, 0x20000180)
T2180 001:041.934 - 0.014ms returns 0
T2180 001:041.948 JLINK_WriteReg(R3, 0x00000000)
T2180 001:041.962 - 0.014ms returns 0
T2180 001:041.977 JLINK_WriteReg(R4, 0x00000000)
T2180 001:041.992 - 0.014ms returns 0
T2180 001:042.006 JLINK_WriteReg(R5, 0x00000000)
T2180 001:042.021 - 0.014ms returns 0
T2180 001:042.036 JLINK_WriteReg(R6, 0x00000000)
T2180 001:042.050 - 0.014ms returns 0
T2180 001:042.064 JLINK_WriteReg(R7, 0x00000000)
T2180 001:042.078 - 0.014ms returns 0
T2180 001:042.093 JLINK_WriteReg(R8, 0x00000000)
T2180 001:042.107 - 0.014ms returns 0
T2180 001:042.121 JLINK_WriteReg(R9, 0x2000017C)
T2180 001:042.135 - 0.014ms returns 0
T2180 001:042.150 JLINK_WriteReg(R10, 0x00000000)
T2180 001:042.164 - 0.013ms returns 0
T2180 001:042.178 JLINK_WriteReg(R11, 0x00000000)
T2180 001:042.192 - 0.014ms returns 0
T2180 001:042.207 JLINK_WriteReg(R12, 0x00000000)
T2180 001:042.221 - 0.014ms returns 0
T2180 001:042.237 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 001:042.251 - 0.014ms returns 0
T2180 001:042.265 JLINK_WriteReg(R14, 0x20000001)
T2180 001:042.280 - 0.014ms returns 0
T2180 001:042.294 JLINK_WriteReg(R15 (PC), 0x20000082)
T2180 001:042.309 - 0.014ms returns 0
T2180 001:042.323 JLINK_WriteReg(XPSR, 0x01000000)
T2180 001:042.337 - 0.014ms returns 0
T2180 001:042.352 JLINK_WriteReg(MSP, 0x20001000)
T2180 001:042.365 - 0.013ms returns 0
T2180 001:042.380 JLINK_WriteReg(PSP, 0x20001000)
T2180 001:042.396 - 0.016ms returns 0
T2180 001:042.427 JLINK_WriteReg(CFBP, 0x00000000)
T2180 001:042.441 - 0.014ms returns 0
T2180 001:042.456 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 001:042.471 - 0.015ms returns 0x0000001C
T2180 001:042.486 JLINK_Go()
T2180 001:042.505   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 001:045.373 - 2.887ms
T2180 001:045.395 JLINK_IsHalted()
T2180 001:047.945   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 001:048.238 - 2.842ms returns TRUE
T2180 001:048.255 JLINK_ReadReg(R15 (PC))
T2180 001:048.270 - 0.014ms returns 0x20000000
T2180 001:048.285 JLINK_ClrBPEx(BPHandle = 0x0000001C)
T2180 001:048.299 - 0.014ms returns 0x00
T2180 001:048.315 JLINK_ReadReg(R0)
T2180 001:048.329 - 0.014ms returns 0x00000000
T2180 001:103.185 JLINK_WriteMem(0x20000000, 0x180 Bytes, ...)
T2180 001:103.214   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T2180 001:103.251   CPU_WriteMem(384 bytes @ 0x20000000)
T2180 001:105.608 - 2.423ms returns 0x180
T2180 001:105.662 JLINK_HasError()
T2180 001:105.680 JLINK_WriteReg(R0, 0x08000000)
T2180 001:105.697 - 0.017ms returns 0
T2180 001:105.712 JLINK_WriteReg(R1, 0x017D7840)
T2180 001:105.725 - 0.013ms returns 0
T2180 001:105.741 JLINK_WriteReg(R2, 0x00000003)
T2180 001:105.755 - 0.014ms returns 0
T2180 001:105.770 JLINK_WriteReg(R3, 0x00000000)
T2180 001:105.784 - 0.014ms returns 0
T2180 001:105.799 JLINK_WriteReg(R4, 0x00000000)
T2180 001:105.813 - 0.013ms returns 0
T2180 001:105.828 JLINK_WriteReg(R5, 0x00000000)
T2180 001:105.842 - 0.014ms returns 0
T2180 001:105.856 JLINK_WriteReg(R6, 0x00000000)
T2180 001:105.870 - 0.014ms returns 0
T2180 001:105.885 JLINK_WriteReg(R7, 0x00000000)
T2180 001:105.899 - 0.014ms returns 0
T2180 001:105.913 JLINK_WriteReg(R8, 0x00000000)
T2180 001:105.927 - 0.014ms returns 0
T2180 001:105.942 JLINK_WriteReg(R9, 0x2000017C)
T2180 001:105.956 - 0.014ms returns 0
T2180 001:105.970 JLINK_WriteReg(R10, 0x00000000)
T2180 001:105.984 - 0.014ms returns 0
T2180 001:105.999 JLINK_WriteReg(R11, 0x00000000)
T2180 001:106.013 - 0.014ms returns 0
T2180 001:106.027 JLINK_WriteReg(R12, 0x00000000)
T2180 001:106.041 - 0.014ms returns 0
T2180 001:106.056 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 001:106.070 - 0.014ms returns 0
T2180 001:106.085 JLINK_WriteReg(R14, 0x20000001)
T2180 001:106.099 - 0.014ms returns 0
T2180 001:106.114 JLINK_WriteReg(R15 (PC), 0x20000054)
T2180 001:106.128 - 0.014ms returns 0
T2180 001:106.142 JLINK_WriteReg(XPSR, 0x01000000)
T2180 001:106.157 - 0.014ms returns 0
T2180 001:106.172 JLINK_WriteReg(MSP, 0x20001000)
T2180 001:106.186 - 0.014ms returns 0
T2180 001:106.200 JLINK_WriteReg(PSP, 0x20001000)
T2180 001:106.215 - 0.014ms returns 0
T2180 001:106.229 JLINK_WriteReg(CFBP, 0x00000000)
T2180 001:106.243 - 0.014ms returns 0
T2180 001:106.258 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 001:106.277   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 001:106.669 - 0.410ms returns 0x0000001D
T2180 001:106.689 JLINK_Go()
T2180 001:106.705   CPU_WriteMem(2 bytes @ 0x20000000)
T2180 001:106.989   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 001:109.834 - 3.145ms
T2180 001:109.852 JLINK_IsHalted()
T2180 001:112.399   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 001:112.686 - 2.833ms returns TRUE
T2180 001:112.706 JLINK_ReadReg(R15 (PC))
T2180 001:112.726 - 0.020ms returns 0x20000000
T2180 001:112.741 JLINK_ClrBPEx(BPHandle = 0x0000001D)
T2180 001:112.756 - 0.014ms returns 0x00
T2180 001:112.770 JLINK_ReadReg(R0)
T2180 001:112.785 - 0.014ms returns 0x00000000
T2180 001:112.800 JLINK_HasError()
T2180 001:112.815 JLINK_WriteReg(R0, 0xFFFFFFFF)
T2180 001:112.829 - 0.014ms returns 0
T2180 001:112.844 JLINK_WriteReg(R1, 0x08000000)
T2180 001:112.857 - 0.014ms returns 0
T2180 001:112.872 JLINK_WriteReg(R2, 0x00005530)
T2180 001:112.886 - 0.014ms returns 0
T2180 001:112.901 JLINK_WriteReg(R3, 0x04C11DB7)
T2180 001:112.915 - 0.014ms returns 0
T2180 001:112.929 JLINK_WriteReg(R4, 0x00000000)
T2180 001:112.943 - 0.014ms returns 0
T2180 001:112.958 JLINK_WriteReg(R5, 0x00000000)
T2180 001:112.972 - 0.013ms returns 0
T2180 001:112.986 JLINK_WriteReg(R6, 0x00000000)
T2180 001:113.000 - 0.013ms returns 0
T2180 001:113.015 JLINK_WriteReg(R7, 0x00000000)
T2180 001:113.029 - 0.013ms returns 0
T2180 001:113.043 JLINK_WriteReg(R8, 0x00000000)
T2180 001:113.060 - 0.016ms returns 0
T2180 001:113.077 JLINK_WriteReg(R9, 0x2000017C)
T2180 001:113.091 - 0.013ms returns 0
T2180 001:113.105 JLINK_WriteReg(R10, 0x00000000)
T2180 001:113.119 - 0.014ms returns 0
T2180 001:113.134 JLINK_WriteReg(R11, 0x00000000)
T2180 001:113.148 - 0.013ms returns 0
T2180 001:113.162 JLINK_WriteReg(R12, 0x00000000)
T2180 001:113.176 - 0.014ms returns 0
T2180 001:113.191 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 001:113.205 - 0.014ms returns 0
T2180 001:113.219 JLINK_WriteReg(R14, 0x20000001)
T2180 001:113.234 - 0.014ms returns 0
T2180 001:113.248 JLINK_WriteReg(R15 (PC), 0x20000002)
T2180 001:113.262 - 0.013ms returns 0
T2180 001:113.276 JLINK_WriteReg(XPSR, 0x01000000)
T2180 001:113.290 - 0.014ms returns 0
T2180 001:113.305 JLINK_WriteReg(MSP, 0x20001000)
T2180 001:113.319 - 0.013ms returns 0
T2180 001:113.333 JLINK_WriteReg(PSP, 0x20001000)
T2180 001:113.347 - 0.014ms returns 0
T2180 001:113.361 JLINK_WriteReg(CFBP, 0x00000000)
T2180 001:113.375 - 0.014ms returns 0
T2180 001:113.390 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 001:113.405 - 0.014ms returns 0x0000001E
T2180 001:113.419 JLINK_Go()
T2180 001:113.437   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 001:116.267 - 2.847ms
T2180 001:116.288 JLINK_IsHalted()
T2180 001:116.557 - 0.268ms returns FALSE
T2180 001:116.574 JLINK_HasError()
T2180 001:121.181 JLINK_IsHalted()
T2180 001:121.483 - 0.302ms returns FALSE
T2180 001:121.500 JLINK_HasError()
T2180 001:123.172 JLINK_IsHalted()
T2180 001:123.426 - 0.253ms returns FALSE
T2180 001:123.442 JLINK_HasError()
T2180 001:125.158 JLINK_IsHalted()
T2180 001:125.436 - 0.277ms returns FALSE
T2180 001:125.452 JLINK_HasError()
T2180 001:127.171 JLINK_IsHalted()
T2180 001:127.461 - 0.291ms returns FALSE
T2180 001:127.477 JLINK_HasError()
T2180 001:129.166 JLINK_IsHalted()
T2180 001:129.442 - 0.276ms returns FALSE
T2180 001:129.458 JLINK_HasError()
T2180 001:131.150 JLINK_IsHalted()
T2180 001:131.431 - 0.280ms returns FALSE
T2180 001:131.447 JLINK_HasError()
T2180 001:133.147 JLINK_IsHalted()
T2180 001:133.477 - 0.329ms returns FALSE
T2180 001:133.508 JLINK_HasError()
T2180 001:136.311 JLINK_IsHalted()
T2180 001:136.673 - 0.361ms returns FALSE
T2180 001:136.697 JLINK_HasError()
T2180 001:138.324 JLINK_IsHalted()
T2180 001:138.626 - 0.301ms returns FALSE
T2180 001:138.650 JLINK_HasError()
T2180 001:140.331 JLINK_IsHalted()
T2180 001:140.630 - 0.298ms returns FALSE
T2180 001:140.654 JLINK_HasError()
T2180 001:142.290 JLINK_IsHalted()
T2180 001:142.588 - 0.297ms returns FALSE
T2180 001:142.612 JLINK_HasError()
T2180 001:144.205 JLINK_IsHalted()
T2180 001:144.476 - 0.270ms returns FALSE
T2180 001:144.500 JLINK_HasError()
T2180 001:146.227 JLINK_IsHalted()
T2180 001:146.500 - 0.274ms returns FALSE
T2180 001:146.516 JLINK_HasError()
T2180 001:148.215 JLINK_IsHalted()
T2180 001:148.497 - 0.283ms returns FALSE
T2180 001:148.520 JLINK_HasError()
T2180 001:150.805 JLINK_IsHalted()
T2180 001:151.157 - 0.351ms returns FALSE
T2180 001:151.181 JLINK_HasError()
T2180 001:152.848 JLINK_IsHalted()
T2180 001:153.261 - 0.412ms returns FALSE
T2180 001:153.322 JLINK_HasError()
T2180 001:154.816 JLINK_IsHalted()
T2180 001:155.264 - 0.447ms returns FALSE
T2180 001:155.298 JLINK_HasError()
T2180 001:156.756 JLINK_IsHalted()
T2180 001:157.152 - 0.395ms returns FALSE
T2180 001:157.174 JLINK_HasError()
T2180 001:158.771 JLINK_IsHalted()
T2180 001:159.069 - 0.298ms returns FALSE
T2180 001:159.114 JLINK_HasError()
T2180 001:160.767 JLINK_IsHalted()
T2180 001:161.024 - 0.256ms returns FALSE
T2180 001:161.040 JLINK_HasError()
T2180 001:162.755 JLINK_IsHalted()
T2180 001:163.009 - 0.254ms returns FALSE
T2180 001:163.025 JLINK_HasError()
T2180 001:165.018 JLINK_IsHalted()
T2180 001:165.288 - 0.270ms returns FALSE
T2180 001:165.308 JLINK_HasError()
T2180 001:168.019 JLINK_IsHalted()
T2180 001:168.286 - 0.268ms returns FALSE
T2180 001:168.302 JLINK_HasError()
T2180 001:169.999 JLINK_IsHalted()
T2180 001:170.285 - 0.287ms returns FALSE
T2180 001:170.305 JLINK_HasError()
T2180 001:171.985 JLINK_IsHalted()
T2180 001:172.261 - 0.276ms returns FALSE
T2180 001:172.276 JLINK_HasError()
T2180 001:173.978 JLINK_IsHalted()
T2180 001:174.260 - 0.283ms returns FALSE
T2180 001:174.276 JLINK_HasError()
T2180 001:175.978 JLINK_IsHalted()
T2180 001:176.261 - 0.284ms returns FALSE
T2180 001:176.277 JLINK_HasError()
T2180 001:178.107 JLINK_IsHalted()
T2180 001:178.402 - 0.295ms returns FALSE
T2180 001:178.420 JLINK_HasError()
T2180 001:179.777 JLINK_IsHalted()
T2180 001:180.058 - 0.280ms returns FALSE
T2180 001:180.080 JLINK_HasError()
T2180 001:182.405 JLINK_IsHalted()
T2180 001:182.681 - 0.275ms returns FALSE
T2180 001:182.697 JLINK_HasError()
T2180 001:184.399 JLINK_IsHalted()
T2180 001:184.657 - 0.257ms returns FALSE
T2180 001:184.672 JLINK_HasError()
T2180 001:186.403 JLINK_IsHalted()
T2180 001:186.658 - 0.256ms returns FALSE
T2180 001:186.675 JLINK_HasError()
T2180 001:188.362 JLINK_IsHalted()
T2180 001:188.608 - 0.246ms returns FALSE
T2180 001:188.624 JLINK_HasError()
T2180 001:190.382 JLINK_IsHalted()
T2180 001:190.638 - 0.256ms returns FALSE
T2180 001:190.654 JLINK_HasError()
T2180 001:192.381 JLINK_IsHalted()
T2180 001:192.637 - 0.257ms returns FALSE
T2180 001:192.653 JLINK_HasError()
T2180 001:194.368 JLINK_IsHalted()
T2180 001:194.723 - 0.355ms returns FALSE
T2180 001:194.744 JLINK_HasError()
T2180 001:196.719 JLINK_IsHalted()
T2180 001:197.043 - 0.325ms returns FALSE
T2180 001:197.059 JLINK_HasError()
T2180 001:198.704 JLINK_IsHalted()
T2180 001:199.022 - 0.319ms returns FALSE
T2180 001:199.038 JLINK_HasError()
T2180 001:200.716 JLINK_IsHalted()
T2180 001:201.015 - 0.298ms returns FALSE
T2180 001:201.031 JLINK_HasError()
T2180 001:202.684 JLINK_IsHalted()
T2180 001:202.974 - 0.290ms returns FALSE
T2180 001:203.010 JLINK_HasError()
T2180 001:204.649 JLINK_IsHalted()
T2180 001:204.938 - 0.289ms returns FALSE
T2180 001:204.954 JLINK_HasError()
T2180 001:206.677 JLINK_IsHalted()
T2180 001:206.989 - 0.313ms returns FALSE
T2180 001:207.005 JLINK_HasError()
T2180 001:208.664 JLINK_IsHalted()
T2180 001:208.957 - 0.292ms returns FALSE
T2180 001:208.973 JLINK_HasError()
T2180 001:210.869 JLINK_IsHalted()
T2180 001:211.225 - 0.355ms returns FALSE
T2180 001:211.242 JLINK_HasError()
T2180 001:213.883 JLINK_IsHalted()
T2180 001:214.225 - 0.341ms returns FALSE
T2180 001:214.241 JLINK_HasError()
T2180 001:215.873 JLINK_IsHalted()
T2180 001:216.225 - 0.352ms returns FALSE
T2180 001:216.241 JLINK_HasError()
T2180 001:217.872 JLINK_IsHalted()
T2180 001:218.225 - 0.354ms returns FALSE
T2180 001:218.270 JLINK_HasError()
T2180 001:219.865 JLINK_IsHalted()
T2180 001:220.225 - 0.361ms returns FALSE
T2180 001:220.240 JLINK_HasError()
T2180 001:221.858 JLINK_IsHalted()
T2180 001:222.224 - 0.366ms returns FALSE
T2180 001:222.240 JLINK_HasError()
T2180 001:223.845 JLINK_IsHalted()
T2180 001:224.139 - 0.293ms returns FALSE
T2180 001:224.173 JLINK_HasError()
T2180 001:226.057 JLINK_IsHalted()
T2180 001:226.429 - 0.371ms returns FALSE
T2180 001:226.445 JLINK_HasError()
T2180 001:229.036 JLINK_IsHalted()
T2180 001:229.431 - 0.395ms returns FALSE
T2180 001:229.447 JLINK_HasError()
T2180 001:231.033 JLINK_IsHalted()
T2180 001:231.447 - 0.414ms returns FALSE
T2180 001:231.466 JLINK_HasError()
T2180 001:232.985 JLINK_IsHalted()
T2180 001:233.326 - 0.340ms returns FALSE
T2180 001:233.343 JLINK_HasError()
T2180 001:235.017 JLINK_IsHalted()
T2180 001:235.310 - 0.293ms returns FALSE
T2180 001:235.326 JLINK_HasError()
T2180 001:236.971 JLINK_IsHalted()
T2180 001:239.612   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 001:239.907 - 2.935ms returns TRUE
T2180 001:239.933 JLINK_ReadReg(R15 (PC))
T2180 001:239.950 - 0.016ms returns 0x20000000
T2180 001:239.966 JLINK_ClrBPEx(BPHandle = 0x0000001E)
T2180 001:239.985 - 0.018ms returns 0x00
T2180 001:240.004 JLINK_ReadReg(R0)
T2180 001:240.024 - 0.020ms returns 0xFDD0E759
T2180 001:241.078 JLINK_HasError()
T2180 001:241.107 JLINK_WriteReg(R0, 0x00000003)
T2180 001:241.126 - 0.018ms returns 0
T2180 001:241.142 JLINK_WriteReg(R1, 0x08000000)
T2180 001:241.156 - 0.014ms returns 0
T2180 001:241.170 JLINK_WriteReg(R2, 0x00005530)
T2180 001:241.184 - 0.014ms returns 0
T2180 001:241.199 JLINK_WriteReg(R3, 0x04C11DB7)
T2180 001:241.213 - 0.013ms returns 0
T2180 001:241.227 JLINK_WriteReg(R4, 0x00000000)
T2180 001:241.241 - 0.014ms returns 0
T2180 001:241.256 JLINK_WriteReg(R5, 0x00000000)
T2180 001:241.270 - 0.014ms returns 0
T2180 001:241.286 JLINK_WriteReg(R6, 0x00000000)
T2180 001:241.301 - 0.014ms returns 0
T2180 001:241.315 JLINK_WriteReg(R7, 0x00000000)
T2180 001:241.329 - 0.014ms returns 0
T2180 001:241.344 JLINK_WriteReg(R8, 0x00000000)
T2180 001:241.358 - 0.013ms returns 0
T2180 001:241.372 JLINK_WriteReg(R9, 0x2000017C)
T2180 001:241.386 - 0.014ms returns 0
T2180 001:241.401 JLINK_WriteReg(R10, 0x00000000)
T2180 001:241.415 - 0.014ms returns 0
T2180 001:241.429 JLINK_WriteReg(R11, 0x00000000)
T2180 001:241.443 - 0.014ms returns 0
T2180 001:241.457 JLINK_WriteReg(R12, 0x00000000)
T2180 001:241.471 - 0.013ms returns 0
T2180 001:241.486 JLINK_WriteReg(R13 (SP), 0x20001000)
T2180 001:241.500 - 0.014ms returns 0
T2180 001:241.514 JLINK_WriteReg(R14, 0x20000001)
T2180 001:241.528 - 0.013ms returns 0
T2180 001:241.542 JLINK_WriteReg(R15 (PC), 0x20000082)
T2180 001:241.557 - 0.014ms returns 0
T2180 001:241.571 JLINK_WriteReg(XPSR, 0x01000000)
T2180 001:241.585 - 0.014ms returns 0
T2180 001:241.599 JLINK_WriteReg(MSP, 0x20001000)
T2180 001:241.613 - 0.014ms returns 0
T2180 001:241.628 JLINK_WriteReg(PSP, 0x20001000)
T2180 001:241.642 - 0.013ms returns 0
T2180 001:241.656 JLINK_WriteReg(CFBP, 0x00000000)
T2180 001:241.670 - 0.013ms returns 0
T2180 001:241.685 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T2180 001:241.700 - 0.015ms returns 0x0000001F
T2180 001:241.715 JLINK_Go()
T2180 001:241.737   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 001:244.596 - 2.880ms
T2180 001:244.618 JLINK_IsHalted()
T2180 001:247.149   CPU_ReadMem(2 bytes @ 0x20000000)
T2180 001:247.445 - 2.826ms returns TRUE
T2180 001:247.461 JLINK_ReadReg(R15 (PC))
T2180 001:247.476 - 0.015ms returns 0x20000000
T2180 001:247.491 JLINK_ClrBPEx(BPHandle = 0x0000001F)
T2180 001:247.506 - 0.014ms returns 0x00
T2180 001:247.521 JLINK_ReadReg(R0)
T2180 001:247.535 - 0.014ms returns 0x00000000
T2180 001:302.205 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
T2180 001:302.253   Data:  FE E7
T2180 001:302.291   CPU_WriteMem(2 bytes @ 0x20000000)
T2180 001:302.649 - 0.443ms returns 0x2
T2180 001:302.671 JLINK_HasError()
T2180 001:302.686 JLINK_HasError()
T2180 001:302.701 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T2180 001:302.716 - 0.014ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T2180 001:302.730 JLINK_Reset()
T2180 001:304.005   Memory map 'before startup completion point' is active
T2180 001:304.044   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T2180 001:304.337   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2180 001:306.504   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T2180 001:308.028   Reset: Reset device via AIRCR.SYSRESETREQ.
T2180 001:308.071   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T2180 001:361.400   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2180 001:361.792   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2180 001:362.088   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2180 001:368.366   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2180 001:371.632   CPU_WriteMem(4 bytes @ 0x********)
T2180 001:372.128   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T2180 001:372.572   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 001:372.981 - 70.250ms
T2180 001:373.003 JLINK_Go()
T2180 001:373.024   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 001:373.304   CPU_WriteMem(4 bytes @ 0xE0002008)
T2180 001:373.328   CPU_WriteMem(4 bytes @ 0xE000200C)
T2180 001:373.349   CPU_WriteMem(4 bytes @ 0xE0002010)
T2180 001:373.369   CPU_WriteMem(4 bytes @ 0xE0002014)
T2180 001:373.390   CPU_WriteMem(4 bytes @ 0xE0002018)
T2180 001:373.411   CPU_WriteMem(4 bytes @ 0xE000201C)
T2180 001:374.846   CPU_WriteMem(4 bytes @ 0xE0001004)
T2180 001:377.360   Memory map 'after startup completion point' is active
T2180 001:377.397 - 4.394ms
T2180 001:388.758 JLINK_Close()
T2180 001:389.062   CPU is running
T2180 001:389.101   CPU_WriteMem(4 bytes @ 0xE0002008)
T2180 001:389.499   CPU is running
T2180 001:389.521   CPU_WriteMem(4 bytes @ 0xE000200C)
T2180 001:389.953   CPU is running
T2180 001:389.975   CPU_WriteMem(4 bytes @ 0xE0002010)
T2180 001:390.325   CPU is running
T2180 001:390.347   CPU_WriteMem(4 bytes @ 0xE0002014)
T2180 001:390.646   CPU is running
T2180 001:390.668   CPU_WriteMem(4 bytes @ 0xE0002018)
T2180 001:390.971   CPU is running
T2180 001:390.992   CPU_WriteMem(4 bytes @ 0xE000201C)
T2180 001:395.801   OnDisconnectTarget() start
T2180 001:395.841    J-Link Script File: Executing OnDisconnectTarget()
T2180 001:395.866   CPU_WriteMem(4 bytes @ 0xE0042004)
T2180 001:396.303   CPU_WriteMem(4 bytes @ 0xE0042008)
T2180 001:397.867   OnDisconnectTarget() end - Took 780us
T2180 001:397.900   CPU_ReadMem(4 bytes @ 0xE0001000)
T2180 001:412.000 - 23.242ms
T2180 001:412.035   
T2180 001:412.049   Closed
