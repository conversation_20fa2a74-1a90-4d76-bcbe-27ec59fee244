Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    startup_stm32f205xx.o(RESET) refers to startup_stm32f205xx.o(STACK) for __initial_sp
    startup_stm32f205xx.o(RESET) refers to startup_stm32f205xx.o(.text) for Reset_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(.text.EXTI9_5_IRQHandler) for EXTI9_5_IRQHandler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(.text.TIM1_UP_TIM10_IRQHandler) for TIM1_UP_TIM10_IRQHandler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(.text.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f205xx.o(.text) refers to system_stm32f2xx.o(.text.SystemInit) for SystemInit
    startup_stm32f205xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(.text.IOUART_PutChar) refers to main.o(.bss.iouart_buffer) for iouart_buffer
    main.o(.ARM.exidx.text.IOUART_PutChar) refers to main.o(.text.IOUART_PutChar) for [Anonymous Symbol]
    main.o(.text.IOUART_SendByte) refers to main.o(.bss.iouart_buffer) for iouart_buffer
    main.o(.text.IOUART_SendByte) refers to main.o(.text.IOUART_PutChar) for IOUART_PutChar
    main.o(.text.IOUART_SendByte) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    main.o(.ARM.exidx.text.IOUART_SendByte) refers to main.o(.text.IOUART_SendByte) for [Anonymous Symbol]
    main.o(.text.IOUART_SendString) refers to main.o(.text.IOUART_PutChar) for IOUART_PutChar
    main.o(.ARM.exidx.text.IOUART_SendString) refers to main.o(.text.IOUART_SendString) for [Anonymous Symbol]
    main.o(.text.HAL_TIM_PeriodElapsedCallback) refers to main.o(.bss.HAL_TIM_PeriodElapsedCallback.global_time_us) for HAL_TIM_PeriodElapsedCallback.global_time_us
    main.o(.text.HAL_TIM_PeriodElapsedCallback) refers to main.o(.bss.iouart_buffer) for iouart_buffer
    main.o(.text.HAL_TIM_PeriodElapsedCallback) refers to main.o(.text.iouart_TXD) for iouart_TXD
    main.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback) refers to main.o(.text.HAL_TIM_PeriodElapsedCallback) for [Anonymous Symbol]
    main.o(.text.iouart_TXD) refers to main.o(.data.iouart_gpio) for iouart_gpio
    main.o(.ARM.exidx.text.iouart_TXD) refers to main.o(.text.iouart_TXD) for [Anonymous Symbol]
    main.o(.text.IOUART_Init) refers to main.o(.data.iouart_gpio) for iouart_gpio
    main.o(.text.IOUART_Init) refers to stm32f2xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    main.o(.text.IOUART_Init) refers to stm32f2xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(.text.IOUART_Init) refers to main.o(.bss.htim2) for htim2
    main.o(.text.IOUART_Init) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    main.o(.text.IOUART_Init) refers to main.o(.bss.htim3) for htim3
    main.o(.ARM.exidx.text.IOUART_Init) refers to main.o(.text.IOUART_Init) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to main.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Error_Handler) refers to main.o(.text.Error_Handler) for [Anonymous Symbol]
    main.o(.text.main) refers to stm32f2xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to main.o(.text.SystemClock_Config) for SystemClock_Config
    main.o(.text.main) refers to main.o(.text.MX_TIM1_Init) for MX_TIM1_Init
    main.o(.text.main) refers to main.o(.text.MX_TIM2_Init) for MX_TIM2_Init
    main.o(.text.main) refers to main.o(.text.MX_TIM3_Init) for MX_TIM3_Init
    main.o(.text.main) refers to main.o(.text.IOUART_Init) for IOUART_Init
    main.o(.text.main) refers to test_board_control.o(.text.TestBoard_Init) for TestBoard_Init
    main.o(.text.main) refers to test_board_control.o(.text.Display_Init) for Display_Init
    main.o(.text.main) refers to main.o(.rodata.str1.1) for .L.str
    main.o(.text.main) refers to test_board_control.o(.text.Display_ShowMessage) for Display_ShowMessage
    main.o(.text.main) refers to test_board_control.o(.text.TestBoard_UpdateStatus) for TestBoard_UpdateStatus
    main.o(.text.main) refers to test_board_control.o(.text.TestBoard_ProcessHeartbeat) for TestBoard_ProcessHeartbeat
    main.o(.text.main) refers to test_board_control.o(.text.Display_UpdateStatus) for Display_UpdateStatus
    main.o(.text.main) refers to main.o(.bss.main.test_executed) for main.test_executed
    main.o(.text.main) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    main.o(.text.main) refers to stm32f2xx_hal.o(.text.HAL_Delay) for HAL_Delay
    main.o(.text.main) refers to test_board_test.o(.text.TestBoard_RunAllTests) for TestBoard_RunAllTests
    main.o(.text.main) refers to main.o(.bss.main.last_test_time) for main.last_test_time
    main.o(.text.main) refers to test_board_control.o(.text.TestBoard_PowerOn) for TestBoard_PowerOn
    main.o(.text.main) refers to test_board_control.o(.text.TestBoard_SetVoltage) for TestBoard_SetVoltage
    main.o(.text.main) refers to test_board_control.o(.text.TestBoard_StartTest) for TestBoard_StartTest
    main.o(.text.main) refers to test_board_control.o(.text.TestBoard_SetCurrent) for TestBoard_SetCurrent
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(.text.SystemClock_Config) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.SystemClock_Config) refers to main.o(.text.Error_Handler) for Error_Handler
    main.o(.text.SystemClock_Config) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.ARM.exidx.text.SystemClock_Config) refers to main.o(.text.SystemClock_Config) for [Anonymous Symbol]
    main.o(.text.MX_TIM1_Init) refers to main.o(.bss.htim1) for htim1
    main.o(.text.MX_TIM1_Init) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    main.o(.text.MX_TIM1_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    main.o(.text.MX_TIM1_Init) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    main.o(.text.MX_TIM1_Init) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    main.o(.ARM.exidx.text.MX_TIM1_Init) refers to main.o(.text.MX_TIM1_Init) for [Anonymous Symbol]
    main.o(.text.MX_TIM2_Init) refers to main.o(.bss.htim2) for htim2
    main.o(.text.MX_TIM2_Init) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    main.o(.text.MX_TIM2_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    main.o(.text.MX_TIM2_Init) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    main.o(.text.MX_TIM2_Init) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    main.o(.ARM.exidx.text.MX_TIM2_Init) refers to main.o(.text.MX_TIM2_Init) for [Anonymous Symbol]
    main.o(.text.MX_TIM3_Init) refers to main.o(.bss.htim3) for htim3
    main.o(.text.MX_TIM3_Init) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    main.o(.text.MX_TIM3_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    main.o(.text.MX_TIM3_Init) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    main.o(.text.MX_TIM3_Init) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    main.o(.ARM.exidx.text.MX_TIM3_Init) refers to main.o(.text.MX_TIM3_Init) for [Anonymous Symbol]
    stm32f2xx_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f2xx_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f2xx_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f2xx_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f2xx_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f2xx_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f2xx_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f2xx_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f2xx_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f2xx_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f2xx_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f2xx_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f2xx_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f2xx_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f2xx_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f2xx_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f2xx_it.o(.text.SysTick_Handler) refers to stm32f2xx_hal.o(.text.HAL_IncTick) for HAL_IncTick
    stm32f2xx_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f2xx_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32f2xx_it.o(.text.TIM1_UP_TIM10_IRQHandler) refers to main.o(.bss.htim1) for htim1
    stm32f2xx_it.o(.text.TIM1_UP_TIM10_IRQHandler) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f2xx_it.o(.ARM.exidx.text.TIM1_UP_TIM10_IRQHandler) refers to stm32f2xx_it.o(.text.TIM1_UP_TIM10_IRQHandler) for [Anonymous Symbol]
    stm32f2xx_it.o(.text.TIM3_IRQHandler) refers to main.o(.bss.htim3) for htim3
    stm32f2xx_it.o(.text.TIM3_IRQHandler) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f2xx_it.o(.ARM.exidx.text.TIM3_IRQHandler) refers to stm32f2xx_it.o(.text.TIM3_IRQHandler) for [Anonymous Symbol]
    stm32f2xx_it.o(.text.EXTI9_5_IRQHandler) refers to stm32f2xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32f2xx_it.o(.ARM.exidx.text.EXTI9_5_IRQHandler) refers to stm32f2xx_it.o(.text.EXTI9_5_IRQHandler) for [Anonymous Symbol]
    stm32f2xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f2xx_hal_msp.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f2xx_hal_msp.o(.text.HAL_TIM_Base_MspInit) refers to stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f2xx_hal_msp.o(.text.HAL_TIM_Base_MspInit) refers to stm32f2xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32f2xx_hal_msp.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to stm32f2xx_hal_msp.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    stm32f2xx_hal_msp.o(.text.HAL_TIM_Base_MspDeInit) refers to stm32f2xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    stm32f2xx_hal_msp.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to stm32f2xx_hal_msp.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    test_board_control.o(.ARM.exidx.text.sum_array) refers to test_board_control.o(.text.sum_array) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_GetTypeName) refers to test_board_control.o(.rodata.str1.1) for .L.str
    test_board_control.o(.ARM.exidx.text.TestBoard_GetTypeName) refers to test_board_control.o(.text.TestBoard_GetTypeName) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_GetStatusName) refers to test_board_control.o(.rodata.str1.1) for .L.str.9
    test_board_control.o(.ARM.exidx.text.TestBoard_GetStatusName) refers to test_board_control.o(.text.TestBoard_GetStatusName) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_Init) refers to test_board_control.o(.bss.board_info) for board_info
    test_board_control.o(.text.TestBoard_Init) refers to test_board_control.o(.bss.display_info) for display_info
    test_board_control.o(.text.TestBoard_Init) refers to memseta.o(.text) for __aeabi_memclr4
    test_board_control.o(.ARM.exidx.text.TestBoard_Init) refers to test_board_control.o(.text.TestBoard_Init) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_BuildPacket) refers to memcpya.o(.text) for __aeabi_memcpy
    test_board_control.o(.text.TestBoard_BuildPacket) refers to test_board_control.o(.text.sum_array) for sum_array
    test_board_control.o(.ARM.exidx.text.TestBoard_BuildPacket) refers to test_board_control.o(.text.TestBoard_BuildPacket) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_ValidatePacket) refers to test_board_control.o(.text.sum_array) for sum_array
    test_board_control.o(.ARM.exidx.text.TestBoard_ValidatePacket) refers to test_board_control.o(.text.TestBoard_ValidatePacket) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_SendPacket) refers to test_board_control.o(.bss.board_info) for board_info
    test_board_control.o(.text.TestBoard_SendPacket) refers to main.o(.text.IOUART_PutChar) for IOUART_PutChar
    test_board_control.o(.ARM.exidx.text.TestBoard_SendPacket) refers to test_board_control.o(.text.TestBoard_SendPacket) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_PowerOn) refers to test_board_control.o(.text.TestBoard_BuildPacket) for TestBoard_BuildPacket
    test_board_control.o(.text.TestBoard_PowerOn) refers to test_board_control.o(.text.TestBoard_SendPacket) for TestBoard_SendPacket
    test_board_control.o(.text.TestBoard_PowerOn) refers to test_board_control.o(.text.update_board_info) for update_board_info
    test_board_control.o(.ARM.exidx.text.TestBoard_PowerOn) refers to test_board_control.o(.text.TestBoard_PowerOn) for [Anonymous Symbol]
    test_board_control.o(.text.update_board_info) refers to test_board_control.o(.bss.board_info) for board_info
    test_board_control.o(.text.update_board_info) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    test_board_control.o(.text.update_board_info) refers to test_board_control.o(.bss.display_info) for display_info
    test_board_control.o(.text.update_board_info) refers to test_board_control.o(.text.TestBoard_GetStatusName) for TestBoard_GetStatusName
    test_board_control.o(.text.update_board_info) refers to strcpy.o(.text) for strcpy
    test_board_control.o(.ARM.exidx.text.update_board_info) refers to test_board_control.o(.text.update_board_info) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_PowerOff) refers to test_board_control.o(.text.TestBoard_BuildPacket) for TestBoard_BuildPacket
    test_board_control.o(.text.TestBoard_PowerOff) refers to test_board_control.o(.text.TestBoard_SendPacket) for TestBoard_SendPacket
    test_board_control.o(.text.TestBoard_PowerOff) refers to test_board_control.o(.text.update_board_info) for update_board_info
    test_board_control.o(.ARM.exidx.text.TestBoard_PowerOff) refers to test_board_control.o(.text.TestBoard_PowerOff) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_Reset) refers to test_board_control.o(.text.TestBoard_BuildPacket) for TestBoard_BuildPacket
    test_board_control.o(.text.TestBoard_Reset) refers to test_board_control.o(.text.TestBoard_SendPacket) for TestBoard_SendPacket
    test_board_control.o(.text.TestBoard_Reset) refers to test_board_control.o(.text.update_board_info) for update_board_info
    test_board_control.o(.ARM.exidx.text.TestBoard_Reset) refers to test_board_control.o(.text.TestBoard_Reset) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_GetStatus) refers to test_board_control.o(.bss.board_info) for board_info
    test_board_control.o(.ARM.exidx.text.TestBoard_GetStatus) refers to test_board_control.o(.text.TestBoard_GetStatus) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_SetVoltage) refers to test_board_control.o(.text.TestBoard_BuildPacket) for TestBoard_BuildPacket
    test_board_control.o(.text.TestBoard_SetVoltage) refers to test_board_control.o(.text.TestBoard_SendPacket) for TestBoard_SendPacket
    test_board_control.o(.text.TestBoard_SetVoltage) refers to test_board_control.o(.bss.board_info) for board_info
    test_board_control.o(.ARM.exidx.text.TestBoard_SetVoltage) refers to test_board_control.o(.text.TestBoard_SetVoltage) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_SetCurrent) refers to test_board_control.o(.text.TestBoard_BuildPacket) for TestBoard_BuildPacket
    test_board_control.o(.text.TestBoard_SetCurrent) refers to test_board_control.o(.text.TestBoard_SendPacket) for TestBoard_SendPacket
    test_board_control.o(.text.TestBoard_SetCurrent) refers to test_board_control.o(.bss.board_info) for board_info
    test_board_control.o(.ARM.exidx.text.TestBoard_SetCurrent) refers to test_board_control.o(.text.TestBoard_SetCurrent) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_StartTest) refers to test_board_control.o(.text.TestBoard_BuildPacket) for TestBoard_BuildPacket
    test_board_control.o(.text.TestBoard_StartTest) refers to test_board_control.o(.text.TestBoard_SendPacket) for TestBoard_SendPacket
    test_board_control.o(.text.TestBoard_StartTest) refers to test_board_control.o(.text.update_board_info) for update_board_info
    test_board_control.o(.text.TestBoard_StartTest) refers to test_board_control.o(.bss.board_info) for board_info
    test_board_control.o(.ARM.exidx.text.TestBoard_StartTest) refers to test_board_control.o(.text.TestBoard_StartTest) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_StopTest) refers to test_board_control.o(.text.TestBoard_BuildPacket) for TestBoard_BuildPacket
    test_board_control.o(.text.TestBoard_StopTest) refers to test_board_control.o(.text.TestBoard_SendPacket) for TestBoard_SendPacket
    test_board_control.o(.text.TestBoard_StopTest) refers to test_board_control.o(.text.update_board_info) for update_board_info
    test_board_control.o(.ARM.exidx.text.TestBoard_StopTest) refers to test_board_control.o(.text.TestBoard_StopTest) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_PowerOnAll) refers to test_board_control.o(.text.TestBoard_PowerOn) for TestBoard_PowerOn
    test_board_control.o(.text.TestBoard_PowerOnAll) refers to stm32f2xx_hal.o(.text.HAL_Delay) for HAL_Delay
    test_board_control.o(.ARM.exidx.text.TestBoard_PowerOnAll) refers to test_board_control.o(.text.TestBoard_PowerOnAll) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_PowerOffAll) refers to test_board_control.o(.text.TestBoard_PowerOff) for TestBoard_PowerOff
    test_board_control.o(.text.TestBoard_PowerOffAll) refers to stm32f2xx_hal.o(.text.HAL_Delay) for HAL_Delay
    test_board_control.o(.ARM.exidx.text.TestBoard_PowerOffAll) refers to test_board_control.o(.text.TestBoard_PowerOffAll) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_EmergencyStop) refers to test_board_control.o(.text.TestBoard_StopTest) for TestBoard_StopTest
    test_board_control.o(.text.TestBoard_EmergencyStop) refers to test_board_control.o(.text.update_board_info) for update_board_info
    test_board_control.o(.ARM.exidx.text.TestBoard_EmergencyStop) refers to test_board_control.o(.text.TestBoard_EmergencyStop) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_GetInfo) refers to test_board_control.o(.bss.board_info) for board_info
    test_board_control.o(.ARM.exidx.text.TestBoard_GetInfo) refers to test_board_control.o(.text.TestBoard_GetInfo) for [Anonymous Symbol]
    test_board_control.o(.text.Display_Init) refers to test_board_control.o(.bss.display_info) for display_info
    test_board_control.o(.text.Display_Init) refers to memseta.o(.text) for __aeabi_memclr4
    test_board_control.o(.text.Display_Init) refers to stm32f2xx_hal.o(.text.HAL_Delay) for HAL_Delay
    test_board_control.o(.text.Display_Init) refers to test_board_control.o(.rodata.str1.1) for .L.str.14
    test_board_control.o(.text.Display_Init) refers to test_board_control.o(.text.Display_SendCommand) for Display_SendCommand
    test_board_control.o(.text.Display_Init) refers to test_board_control.o(.text.Display_ShowMessage) for Display_ShowMessage
    test_board_control.o(.ARM.exidx.text.Display_Init) refers to test_board_control.o(.text.Display_Init) for [Anonymous Symbol]
    test_board_control.o(.text.Display_SendCommand) refers to main.o(.text.IOUART_SendString) for IOUART_SendString
    test_board_control.o(.text.Display_SendCommand) refers to main.o(.text.IOUART_SendByte) for IOUART_SendByte
    test_board_control.o(.ARM.exidx.text.Display_SendCommand) refers to test_board_control.o(.text.Display_SendCommand) for [Anonymous Symbol]
    test_board_control.o(.text.Display_ShowMessage) refers to test_board_control.o(.rodata.str1.1) for .L.str.26
    test_board_control.o(.text.Display_ShowMessage) refers to test_board_control.o(.text.Display_SetComponent) for Display_SetComponent
    test_board_control.o(.ARM.exidx.text.Display_ShowMessage) refers to test_board_control.o(.text.Display_ShowMessage) for [Anonymous Symbol]
    test_board_control.o(.text.Display_SetPage) refers to test_board_control.o(.rodata.str1.1) for .L.str.17
    test_board_control.o(.text.Display_SetPage) refers to printfa.o(i.__0snprintf) for snprintf
    test_board_control.o(.text.Display_SetPage) refers to test_board_control.o(.text.Display_SendCommand) for Display_SendCommand
    test_board_control.o(.ARM.exidx.text.Display_SetPage) refers to test_board_control.o(.text.Display_SetPage) for [Anonymous Symbol]
    test_board_control.o(.text.Display_SetComponent) refers to test_board_control.o(.rodata.str1.1) for .L.str.18
    test_board_control.o(.text.Display_SetComponent) refers to printfa.o(i.__0snprintf) for snprintf
    test_board_control.o(.text.Display_SetComponent) refers to test_board_control.o(.text.Display_SendCommand) for Display_SendCommand
    test_board_control.o(.ARM.exidx.text.Display_SetComponent) refers to test_board_control.o(.text.Display_SetComponent) for [Anonymous Symbol]
    test_board_control.o(.text.Display_UpdateStatus) refers to test_board_control.o(.bss.display_info) for display_info
    test_board_control.o(.text.Display_UpdateStatus) refers to test_board_control.o(.rodata.str1.1) for .L.str.19
    test_board_control.o(.text.Display_UpdateStatus) refers to printfa.o(i.__0snprintf) for snprintf
    test_board_control.o(.text.Display_UpdateStatus) refers to test_board_control.o(.text.Display_SetComponent) for Display_SetComponent
    test_board_control.o(.text.Display_UpdateStatus) refers to test_board_control.o(.text.TestBoard_GetTypeName) for TestBoard_GetTypeName
    test_board_control.o(.ARM.exidx.text.Display_UpdateStatus) refers to test_board_control.o(.text.Display_UpdateStatus) for [Anonymous Symbol]
    test_board_control.o(.text.Display_ShowBoardStatus) refers to test_board_control.o(.bss.display_info) for display_info
    test_board_control.o(.text.Display_ShowBoardStatus) refers to strcpy.o(.text) for strcpy
    test_board_control.o(.ARM.exidx.text.Display_ShowBoardStatus) refers to test_board_control.o(.text.Display_ShowBoardStatus) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_UpdateStatus) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    test_board_control.o(.text.TestBoard_UpdateStatus) refers to test_board_control.o(.bss.display_info) for display_info
    test_board_control.o(.text.TestBoard_UpdateStatus) refers to test_board_control.o(.bss.board_info) for board_info
    test_board_control.o(.text.TestBoard_UpdateStatus) refers to test_board_control.o(.rodata.str1.1) for .L.str.27
    test_board_control.o(.text.TestBoard_UpdateStatus) refers to strcpy.o(.text) for strcpy
    test_board_control.o(.ARM.exidx.text.TestBoard_UpdateStatus) refers to test_board_control.o(.text.TestBoard_UpdateStatus) for [Anonymous Symbol]
    test_board_control.o(.text.TestBoard_ProcessHeartbeat) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    test_board_control.o(.text.TestBoard_ProcessHeartbeat) refers to test_board_control.o(.bss.TestBoard_ProcessHeartbeat.last_heartbeat_time) for TestBoard_ProcessHeartbeat.last_heartbeat_time
    test_board_control.o(.text.TestBoard_ProcessHeartbeat) refers to test_board_control.o(.bss.board_info) for board_info
    test_board_control.o(.text.TestBoard_ProcessHeartbeat) refers to test_board_control.o(.text.TestBoard_BuildPacket) for TestBoard_BuildPacket
    test_board_control.o(.text.TestBoard_ProcessHeartbeat) refers to test_board_control.o(.text.TestBoard_SendPacket) for TestBoard_SendPacket
    test_board_control.o(.ARM.exidx.text.TestBoard_ProcessHeartbeat) refers to test_board_control.o(.text.TestBoard_ProcessHeartbeat) for [Anonymous Symbol]
    test_board_test.o(.text.TestBoard_RunAllTests) refers to test_board_test.o(.bss.test_result) for test_result
    test_board_test.o(.text.TestBoard_RunAllTests) refers to test_board_test.o(.rodata.str1.1) for .L.str
    test_board_test.o(.text.TestBoard_RunAllTests) refers to test_board_control.o(.text.Display_ShowMessage) for Display_ShowMessage
    test_board_test.o(.text.TestBoard_RunAllTests) refers to test_board_test.o(.text.test_uart_communication) for test_uart_communication
    test_board_test.o(.text.TestBoard_RunAllTests) refers to test_board_test.o(.text.test_packet_validation) for test_packet_validation
    test_board_test.o(.text.TestBoard_RunAllTests) refers to test_board_test.o(.text.test_board_control_commands) for test_board_control_commands
    test_board_test.o(.text.TestBoard_RunAllTests) refers to test_board_test.o(.text.test_display_functions) for test_display_functions
    test_board_test.o(.text.TestBoard_RunAllTests) refers to test_board_test.o(.text.test_error_handling) for test_error_handling
    test_board_test.o(.text.TestBoard_RunAllTests) refers to test_board_test.o(.text.print_test_summary) for print_test_summary
    test_board_test.o(.text.TestBoard_RunAllTests) refers to printfa.o(i.__0snprintf) for snprintf
    test_board_test.o(.ARM.exidx.text.TestBoard_RunAllTests) refers to test_board_test.o(.text.TestBoard_RunAllTests) for [Anonymous Symbol]
    test_board_test.o(.text.test_uart_communication) refers to test_board_test.o(.rodata.str1.1) for .L.str.2
    test_board_test.o(.text.test_uart_communication) refers to printfa.o(i.__0snprintf) for snprintf
    test_board_test.o(.text.test_uart_communication) refers to main.o(.text.IOUART_SendString) for IOUART_SendString
    test_board_test.o(.text.test_uart_communication) refers to stm32f2xx_hal.o(.text.HAL_Delay) for HAL_Delay
    test_board_test.o(.text.test_uart_communication) refers to test_board_test.o(.bss.test_result) for test_result
    test_board_test.o(.text.test_uart_communication) refers to printfa.o(i.__0printf) for printf
    test_board_test.o(.ARM.exidx.text.test_uart_communication) refers to test_board_test.o(.text.test_uart_communication) for [Anonymous Symbol]
    test_board_test.o(.text.test_packet_validation) refers to test_board_control.o(.text.TestBoard_BuildPacket) for TestBoard_BuildPacket
    test_board_test.o(.text.test_packet_validation) refers to test_board_test.o(.bss.test_result) for test_result
    test_board_test.o(.text.test_packet_validation) refers to test_board_test.o(.rodata.str1.1) for .L.str.3
    test_board_test.o(.text.test_packet_validation) refers to printfa.o(i.__0printf) for printf
    test_board_test.o(.text.test_packet_validation) refers to test_board_control.o(.text.TestBoard_ValidatePacket) for TestBoard_ValidatePacket
    test_board_test.o(.text.test_packet_validation) refers to memcmp.o(.text) for memcmp
    test_board_test.o(.ARM.exidx.text.test_packet_validation) refers to test_board_test.o(.text.test_packet_validation) for [Anonymous Symbol]
    test_board_test.o(.text.test_board_control_commands) refers to test_board_control.o(.text.TestBoard_GetInfo) for TestBoard_GetInfo
    test_board_test.o(.text.test_board_control_commands) refers to test_board_test.o(.bss.test_result) for test_result
    test_board_test.o(.text.test_board_control_commands) refers to test_board_test.o(.rodata.str1.1) for .L.str.3
    test_board_test.o(.text.test_board_control_commands) refers to printfa.o(i.__0printf) for printf
    test_board_test.o(.text.test_board_control_commands) refers to test_board_control.o(.text.TestBoard_PowerOn) for TestBoard_PowerOn
    test_board_test.o(.text.test_board_control_commands) refers to stm32f2xx_hal.o(.text.HAL_Delay) for HAL_Delay
    test_board_test.o(.text.test_board_control_commands) refers to test_board_control.o(.text.TestBoard_GetStatus) for TestBoard_GetStatus
    test_board_test.o(.text.test_board_control_commands) refers to test_board_control.o(.text.TestBoard_SetVoltage) for TestBoard_SetVoltage
    test_board_test.o(.text.test_board_control_commands) refers to test_board_control.o(.text.TestBoard_SetCurrent) for TestBoard_SetCurrent
    test_board_test.o(.text.test_board_control_commands) refers to test_board_control.o(.text.TestBoard_StartTest) for TestBoard_StartTest
    test_board_test.o(.text.test_board_control_commands) refers to test_board_control.o(.text.TestBoard_StopTest) for TestBoard_StopTest
    test_board_test.o(.text.test_board_control_commands) refers to test_board_control.o(.text.TestBoard_Reset) for TestBoard_Reset
    test_board_test.o(.text.test_board_control_commands) refers to test_board_control.o(.text.TestBoard_PowerOff) for TestBoard_PowerOff
    test_board_test.o(.text.test_board_control_commands) refers to test_board_control.o(.text.TestBoard_PowerOnAll) for TestBoard_PowerOnAll
    test_board_test.o(.text.test_board_control_commands) refers to test_board_control.o(.text.TestBoard_PowerOffAll) for TestBoard_PowerOffAll
    test_board_test.o(.ARM.exidx.text.test_board_control_commands) refers to test_board_test.o(.text.test_board_control_commands) for [Anonymous Symbol]
    test_board_test.o(.text.test_display_functions) refers to test_board_test.o(.rodata.str1.1) for .L.str.28
    test_board_test.o(.text.test_display_functions) refers to printfa.o(i.__0printf) for printf
    test_board_test.o(.text.test_display_functions) refers to test_board_control.o(.text.Display_ShowMessage) for Display_ShowMessage
    test_board_test.o(.text.test_display_functions) refers to stm32f2xx_hal.o(.text.HAL_Delay) for HAL_Delay
    test_board_test.o(.text.test_display_functions) refers to printfa.o(i.__0snprintf) for snprintf
    test_board_test.o(.text.test_display_functions) refers to test_board_control.o(.text.Display_ShowBoardStatus) for Display_ShowBoardStatus
    test_board_test.o(.text.test_display_functions) refers to test_board_control.o(.text.TestBoard_UpdateStatus) for TestBoard_UpdateStatus
    test_board_test.o(.text.test_display_functions) refers to test_board_control.o(.text.Display_UpdateStatus) for Display_UpdateStatus
    test_board_test.o(.text.test_display_functions) refers to test_board_test.o(.bss.test_result) for test_result
    test_board_test.o(.ARM.exidx.text.test_display_functions) refers to test_board_test.o(.text.test_display_functions) for [Anonymous Symbol]
    test_board_test.o(.text.test_error_handling) refers to test_board_test.o(.rodata.str1.1) for .L.str.32
    test_board_test.o(.text.test_error_handling) refers to printfa.o(i.__0printf) for printf
    test_board_test.o(.text.test_error_handling) refers to test_board_control.o(.text.TestBoard_PowerOn) for TestBoard_PowerOn
    test_board_test.o(.text.test_error_handling) refers to test_board_test.o(.bss.test_result) for test_result
    test_board_test.o(.text.test_error_handling) refers to test_board_control.o(.text.TestBoard_BuildPacket) for TestBoard_BuildPacket
    test_board_test.o(.text.test_error_handling) refers to test_board_control.o(.text.TestBoard_GetStatus) for TestBoard_GetStatus
    test_board_test.o(.text.test_error_handling) refers to test_board_control.o(.text.TestBoard_EmergencyStop) for TestBoard_EmergencyStop
    test_board_test.o(.ARM.exidx.text.test_error_handling) refers to test_board_test.o(.text.test_error_handling) for [Anonymous Symbol]
    test_board_test.o(.ARM.exidx.text.print_test_summary) refers to test_board_test.o(.text.print_test_summary) for [Anonymous Symbol]
    test_board_test.o(.text.TestBoard_GetTestResult) refers to test_board_test.o(.bss.test_result) for test_result
    test_board_test.o(.ARM.exidx.text.TestBoard_GetTestResult) refers to test_board_test.o(.text.TestBoard_GetTestResult) for [Anonymous Symbol]
    test_board_test.o(.text.TestBoard_ResetTestResult) refers to test_board_test.o(.bss.test_result) for test_result
    test_board_test.o(.ARM.exidx.text.TestBoard_ResetTestResult) refers to test_board_test.o(.text.TestBoard_ResetTestResult) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to stm32f2xx_hal_msp.o(.text.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to stm32f2xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Init) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig) refers to stm32f2xx_hal_tim.o(.text.TIM_Base_SetConfig) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) refers to stm32f2xx_hal_msp.o(.text.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Start) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) refers to main.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt) refers to stm32f2xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f2xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.TIM_DMAError) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError) refers to stm32f2xx_hal_tim.o(.text.TIM_DMAError) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32f2xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Init) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Start) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Start) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Stop) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Stop) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt) refers to stm32f2xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt) refers to stm32f2xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32f2xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Start) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Start) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Stop) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Stop) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32f2xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Init) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Start) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Start) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Stop) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Stop) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.TIM_DMACaptureCplt) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt) refers to stm32f2xx_hal_tim.o(.text.TIM_DMACaptureCplt) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt) refers to stm32f2xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32f2xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to stm32f2xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Start) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Start) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to main.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IRQHandler) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_OC1_SetConfig) refers to stm32f2xx_hal_tim.o(.text.TIM_OC1_SetConfig) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig) refers to stm32f2xx_hal_tim.o(.text.TIM_OC2_SetConfig) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_OC3_SetConfig) refers to stm32f2xx_hal_tim.o(.text.TIM_OC3_SetConfig) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_OC4_SetConfig) refers to stm32f2xx_hal_tim.o(.text.TIM_OC4_SetConfig) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig) refers to stm32f2xx_hal_tim.o(.text.TIM_TI1_SetConfig) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_TI2_SetConfig) refers to stm32f2xx_hal_tim.o(.text.TIM_TI2_SetConfig) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_TI3_SetConfig) refers to stm32f2xx_hal_tim.o(.text.TIM_TI3_SetConfig) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_TI4_SetConfig) refers to stm32f2xx_hal_tim.o(.text.TIM_TI4_SetConfig) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f2xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f2xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f2xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f2xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f2xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f2xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f2xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f2xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f2xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.TIM_DMATriggerCplt) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt) refers to stm32f2xx_hal_tim.o(.text.TIM_DMATriggerCplt) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt) refers to stm32f2xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f2xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f2xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f2xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f2xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f2xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f2xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f2xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f2xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f2xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_GenerateEvent) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear) refers to stm32f2xx_hal_tim.o(.text.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig) refers to stm32f2xx_hal_tim.o(.text.TIM_ETR_SetConfig) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) refers to stm32f2xx_hal_tim.o(.text.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f2xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) refers to stm32f2xx_hal_tim.o(.text.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f2xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) refers to stm32f2xx_hal_tim.o(.text.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f2xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) refers to stm32f2xx_hal_tim.o(.text.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_ConfigInputStage) refers to stm32f2xx_hal_tim.o(.text.TIM_TI1_ConfigInputStage) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_ITRx_SetConfig) refers to stm32f2xx_hal_tim.o(.text.TIM_ITRx_SetConfig) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_TI2_ConfigInputStage) refers to stm32f2xx_hal_tim.o(.text.TIM_TI2_ConfigInputStage) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f2xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) refers to stm32f2xx_hal_tim.o(.text.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f2xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) refers to stm32f2xx_hal_tim.o(.text.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f2xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) refers to stm32f2xx_hal_tim.o(.text.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig) refers to stm32f2xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f2xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_GetState) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_GetState) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_GetState) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_GetState) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_GetChannelState) for [Anonymous Symbol]
    stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurstState) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f2xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f2xx_hal_tim.o(.text.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f2xx_hal_tim.o(.text.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.TIM_CCxNChannelCmd) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) refers to stm32f2xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f2xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f2xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f2xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f2xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt) refers to stm32f2xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt) refers to stm32f2xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState) for [Anonymous Symbol]
    stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState) refers to stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState) for [Anonymous Symbol]
    stm32f2xx_hal.o(.text.HAL_Init) refers to stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f2xx_hal.o(.text.HAL_Init) refers to stm32f2xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f2xx_hal.o(.text.HAL_Init) refers to stm32f2xx_hal_msp.o(.text.HAL_MspInit) for HAL_MspInit
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32f2xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32f2xx_hal.o(.text.HAL_InitTick) refers to system_stm32f2xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f2xx_hal.o(.text.HAL_InitTick) refers to stm32f2xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f2xx_hal.o(.text.HAL_InitTick) refers to stm32f2xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f2xx_hal.o(.text.HAL_InitTick) refers to stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f2xx_hal.o(.text.HAL_InitTick) refers to stm32f2xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32f2xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f2xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f2xx_hal.o(.text.HAL_DeInit) refers to stm32f2xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32f2xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32f2xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32f2xx_hal.o(.text.HAL_IncTick) refers to stm32f2xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f2xx_hal.o(.text.HAL_IncTick) refers to stm32f2xx_hal.o(.bss.uwTick) for uwTick
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32f2xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32f2xx_hal.o(.text.HAL_GetTick) refers to stm32f2xx_hal.o(.bss.uwTick) for uwTick
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32f2xx_hal.o(.text.HAL_GetTickPrio) refers to stm32f2xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32f2xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32f2xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f2xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f2xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f2xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f2xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f2xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32f2xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32f2xx_hal.o(.text.HAL_GetTickFreq) refers to stm32f2xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32f2xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32f2xx_hal.o(.text.HAL_Delay) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal.o(.text.HAL_Delay) refers to stm32f2xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32f2xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32f2xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32f2xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32f2xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32f2xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32f2xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode) refers to stm32f2xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode) for [Anonymous Symbol]
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode) refers to stm32f2xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode) for [Anonymous Symbol]
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32f2xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32f2xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32f2xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32f2xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell) refers to stm32f2xx_hal.o(.text.HAL_EnableCompensationCell) for [Anonymous Symbol]
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell) refers to stm32f2xx_hal.o(.text.HAL_DisableCompensationCell) for [Anonymous Symbol]
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0) refers to stm32f2xx_hal.o(.text.HAL_GetUIDw0) for [Anonymous Symbol]
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1) refers to stm32f2xx_hal.o(.text.HAL_GetUIDw1) for [Anonymous Symbol]
    stm32f2xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2) refers to stm32f2xx_hal.o(.text.HAL_GetUIDw2) for [Anonymous Symbol]
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to system_stm32f2xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to stm32f2xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to stm32f2xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f2xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f2xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f2xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f2xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32f2xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_DisableCSS) for [Anonymous Symbol]
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32f2xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f2xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f2xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for [Anonymous Symbol]
    stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32f2xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32f2xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32f2xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f2xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for [Anonymous Symbol]
    stm32f2xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig) refers to stm32f2xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig) for [Anonymous Symbol]
    stm32f2xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f2xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) for [Anonymous Symbol]
    stm32f2xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f2xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_SetPriorityGrouping) for __NVIC_SetPriorityGrouping
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping) refers to stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SetPriorityGrouping) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) refers to stm32f2xx_hal_cortex.o(.text.NVIC_EncodePriority) for NVIC_EncodePriority
    stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_GetPriorityGrouping) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SetPriority) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_SetPriority) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.NVIC_EncodePriority) refers to stm32f2xx_hal_cortex.o(.text.NVIC_EncodePriority) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32f2xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_DisableIRQ) for __NVIC_DisableIRQ
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32f2xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_DisableIRQ) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.text.HAL_SYSTICK_Config) refers to stm32f2xx_hal_cortex.o(.text.SysTick_Config) for SysTick_Config
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32f2xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.text.SysTick_Config) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.SysTick_Config) refers to stm32f2xx_hal_cortex.o(.text.SysTick_Config) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable) refers to stm32f2xx_hal_cortex.o(.text.HAL_MPU_Disable) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable) refers to stm32f2xx_hal_cortex.o(.text.HAL_MPU_Enable) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion) refers to stm32f2xx_hal_cortex.o(.text.HAL_MPU_EnableRegion) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion) refers to stm32f2xx_hal_cortex.o(.text.HAL_MPU_DisableRegion) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion) refers to stm32f2xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping) refers to stm32f2xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_GetPriority) for __NVIC_GetPriority
    stm32f2xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) refers to stm32f2xx_hal_cortex.o(.text.NVIC_DecodePriority) for NVIC_DecodePriority
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32f2xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.NVIC_DecodePriority) refers to stm32f2xx_hal_cortex.o(.text.NVIC_DecodePriority) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_GetPriority) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_GetPriority) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_SetPendingIRQ) for __NVIC_SetPendingIRQ
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SetPendingIRQ) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_GetPendingIRQ) for __NVIC_GetPendingIRQ
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32f2xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_GetPendingIRQ) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f2xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.text.HAL_NVIC_GetActive) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_GetActive) for __NVIC_GetActive
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive) refers to stm32f2xx_hal_cortex.o(.text.HAL_NVIC_GetActive) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_GetActive) refers to stm32f2xx_hal_cortex.o(.text.__NVIC_GetActive) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32f2xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32f2xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32f2xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32f2xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f2xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f2xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f2xx_hal_flash.o(.text.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f2xx_hal_flash.o(.text.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f2xx_hal_flash.o(.text.FLASH_Program_Word) for FLASH_Program_Word
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f2xx_hal_flash.o(.text.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program) refers to stm32f2xx_hal_flash.o(.text.HAL_FLASH_Program) for [Anonymous Symbol]
    stm32f2xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f2xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f2xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f2xx_hal_flash.o(.text.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f2xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f2xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f2xx_hal_flash.o(.ARM.exidx.text.FLASH_Program_Byte) refers to stm32f2xx_hal_flash.o(.text.FLASH_Program_Byte) for [Anonymous Symbol]
    stm32f2xx_hal_flash.o(.ARM.exidx.text.FLASH_Program_HalfWord) refers to stm32f2xx_hal_flash.o(.text.FLASH_Program_HalfWord) for [Anonymous Symbol]
    stm32f2xx_hal_flash.o(.ARM.exidx.text.FLASH_Program_Word) refers to stm32f2xx_hal_flash.o(.text.FLASH_Program_Word) for [Anonymous Symbol]
    stm32f2xx_hal_flash.o(.ARM.exidx.text.FLASH_Program_DoubleWord) refers to stm32f2xx_hal_flash.o(.text.FLASH_Program_DoubleWord) for [Anonymous Symbol]
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f2xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f2xx_hal_flash.o(.text.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f2xx_hal_flash.o(.text.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f2xx_hal_flash.o(.text.FLASH_Program_Word) for FLASH_Program_Word
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f2xx_hal_flash.o(.text.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT) refers to stm32f2xx_hal_flash.o(.text.HAL_FLASH_Program_IT) for [Anonymous Symbol]
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f2xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f2xx_hal_flash.o(.text.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f2xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f2xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler) refers to stm32f2xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) for [Anonymous Symbol]
    stm32f2xx_hal_flash.o(.text.FLASH_SetErrorCode) refers to stm32f2xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f2xx_hal_flash.o(.ARM.exidx.text.FLASH_SetErrorCode) refers to stm32f2xx_hal_flash.o(.text.FLASH_SetErrorCode) for [Anonymous Symbol]
    stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback) refers to stm32f2xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for [Anonymous Symbol]
    stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback) refers to stm32f2xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for [Anonymous Symbol]
    stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock) refers to stm32f2xx_hal_flash.o(.text.HAL_FLASH_Unlock) for [Anonymous Symbol]
    stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock) refers to stm32f2xx_hal_flash.o(.text.HAL_FLASH_Lock) for [Anonymous Symbol]
    stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock) refers to stm32f2xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock) for [Anonymous Symbol]
    stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock) refers to stm32f2xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) for [Anonymous Symbol]
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32f2xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch) refers to stm32f2xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) for [Anonymous Symbol]
    stm32f2xx_hal_flash.o(.text.HAL_FLASH_GetError) refers to stm32f2xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError) refers to stm32f2xx_hal_flash.o(.text.HAL_FLASH_GetError) for [Anonymous Symbol]
    stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f2xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f2xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_MassErase) for FLASH_MassErase
    stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase) refers to stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for [Anonymous Symbol]
    stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_MassErase) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_MassErase) for [Anonymous Symbol]
    stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for [Anonymous Symbol]
    stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for [Anonymous Symbol]
    stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f2xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_MassErase) for FLASH_MassErase
    stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT) refers to stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) for [Anonymous Symbol]
    stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f2xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_BOR_LevelConfig) for FLASH_OB_BOR_LevelConfig
    stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram) refers to stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) for [Anonymous Symbol]
    stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_EnableWRP) refers to stm32f2xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_EnableWRP) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_EnableWRP) for [Anonymous Symbol]
    stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_DisableWRP) refers to stm32f2xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_DisableWRP) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_DisableWRP) for [Anonymous Symbol]
    stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_RDP_LevelConfig) refers to stm32f2xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_RDP_LevelConfig) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_RDP_LevelConfig) for [Anonymous Symbol]
    stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_UserConfig) refers to stm32f2xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_UserConfig) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_UserConfig) for [Anonymous Symbol]
    stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_BOR_LevelConfig) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_BOR_LevelConfig) for [Anonymous Symbol]
    stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_GetBOR) for FLASH_OB_GetBOR
    stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig) refers to stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) for [Anonymous Symbol]
    stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_GetWRP) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_GetWRP) for [Anonymous Symbol]
    stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_GetRDP) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_GetRDP) for [Anonymous Symbol]
    stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_GetUser) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_GetUser) for [Anonymous Symbol]
    stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_GetBOR) refers to stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_GetBOR) for [Anonymous Symbol]
    stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32f2xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32f2xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32f2xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD) refers to stm32f2xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD) for [Anonymous Symbol]
    stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD) refers to stm32f2xx_hal_pwr.o(.text.HAL_PWR_EnablePVD) for [Anonymous Symbol]
    stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD) refers to stm32f2xx_hal_pwr.o(.text.HAL_PWR_DisablePVD) for [Anonymous Symbol]
    stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32f2xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32f2xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32f2xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32f2xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32f2xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f2xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) refers to stm32f2xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler) refers to stm32f2xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) for [Anonymous Symbol]
    stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback) refers to stm32f2xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for [Anonymous Symbol]
    stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32f2xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32f2xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32f2xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32f2xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    stm32f2xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg) refers to stm32f2xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) for [Anonymous Symbol]
    stm32f2xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg) refers to stm32f2xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) for [Anonymous Symbol]
    stm32f2xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown) refers to stm32f2xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown) for [Anonymous Symbol]
    stm32f2xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown) refers to stm32f2xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown) for [Anonymous Symbol]
    stm32f2xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32f2xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32f2xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32f2xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32f2xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32f2xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32f2xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32f2xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32f2xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32f2xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32f2xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32f2xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32f2xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to main.o(.text.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f2xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f2xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f2xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to stm32f2xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    stm32f2xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f2xx_hal_dma.o(.text.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f2xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f2xx_hal_dma.o(.text.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32f2xx_hal_dma.o(.ARM.exidx.text.DMA_CheckFifoParam) refers to stm32f2xx_hal_dma.o(.text.DMA_CheckFifoParam) for [Anonymous Symbol]
    stm32f2xx_hal_dma.o(.text.DMA_CalcBaseAndBitshift) refers to stm32f2xx_hal_dma.o(.rodata.DMA_CalcBaseAndBitshift.flagBitshiftOffset) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f2xx_hal_dma.o(.ARM.exidx.text.DMA_CalcBaseAndBitshift) refers to stm32f2xx_hal_dma.o(.text.DMA_CalcBaseAndBitshift) for [Anonymous Symbol]
    stm32f2xx_hal_dma.o(.text.HAL_DMA_DeInit) refers to stm32f2xx_hal_dma.o(.text.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32f2xx_hal_dma.o(.text.HAL_DMA_Start) refers to stm32f2xx_hal_dma.o(.text.DMA_SetConfig) for DMA_SetConfig
    stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32f2xx_hal_dma.o(.ARM.exidx.text.DMA_SetConfig) refers to stm32f2xx_hal_dma.o(.text.DMA_SetConfig) for [Anonymous Symbol]
    stm32f2xx_hal_dma.o(.text.HAL_DMA_Start_IT) refers to stm32f2xx_hal_dma.o(.text.DMA_SetConfig) for DMA_SetConfig
    stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32f2xx_hal_dma.o(.text.HAL_DMA_Abort) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32f2xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f2xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32f2xx_hal_dma.o(.text.HAL_DMA_IRQHandler) refers to system_stm32f2xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32f2xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32f2xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart) refers to stm32f2xx_hal_dma_ex.o(.text.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f2xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart) refers to stm32f2xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart) for [Anonymous Symbol]
    stm32f2xx_hal_dma_ex.o(.ARM.exidx.text.DMA_MultiBufferSetConfig) refers to stm32f2xx_hal_dma_ex.o(.text.DMA_MultiBufferSetConfig) for [Anonymous Symbol]
    stm32f2xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f2xx_hal_dma_ex.o(.text.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f2xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f2xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT) for [Anonymous Symbol]
    stm32f2xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory) refers to stm32f2xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory) for [Anonymous Symbol]
    stm32f2xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine) refers to stm32f2xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine) for [Anonymous Symbol]
    stm32f2xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine) refers to stm32f2xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine) for [Anonymous Symbol]
    stm32f2xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine) refers to stm32f2xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine) for [Anonymous Symbol]
    stm32f2xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback) refers to stm32f2xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback) for [Anonymous Symbol]
    stm32f2xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle) refers to stm32f2xx_hal_exti.o(.text.HAL_EXTI_GetHandle) for [Anonymous Symbol]
    stm32f2xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler) refers to stm32f2xx_hal_exti.o(.text.HAL_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f2xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending) refers to stm32f2xx_hal_exti.o(.text.HAL_EXTI_GetPending) for [Anonymous Symbol]
    stm32f2xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending) refers to stm32f2xx_hal_exti.o(.text.HAL_EXTI_ClearPending) for [Anonymous Symbol]
    stm32f2xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI) refers to stm32f2xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI) for [Anonymous Symbol]
    system_stm32f2xx.o(.ARM.exidx.text.SystemInit) refers to system_stm32f2xx.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f2xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f2xx.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f2xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f2xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f2xx.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f2xx.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f205xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f205xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    fputc_h.o(i._fputc$hlt) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc_h.o(i._fputc$hlt) refers (Special) to semi.o(.text) for __semihosting_library_function
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f205xx.o(HEAP), (512 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.IOUART_PutChar), (8 bytes).
    Removing main.o(.ARM.exidx.text.IOUART_SendByte), (8 bytes).
    Removing main.o(.ARM.exidx.text.IOUART_SendString), (8 bytes).
    Removing main.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback), (8 bytes).
    Removing main.o(.ARM.exidx.text.iouart_TXD), (8 bytes).
    Removing main.o(.ARM.exidx.text.IOUART_Init), (8 bytes).
    Removing main.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing main.o(.ARM.exidx.text.Error_Handler), (8 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.SystemClock_Config), (8 bytes).
    Removing main.o(.ARM.exidx.text.MX_TIM1_Init), (8 bytes).
    Removing main.o(.ARM.exidx.text.MX_TIM2_Init), (8 bytes).
    Removing main.o(.ARM.exidx.text.MX_TIM3_Init), (8 bytes).
    Removing main.o(.bss.recvData), (1 bytes).
    Removing main.o(.bss.recvStat), (1 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing stm32f2xx_it.o(.text), (0 bytes).
    Removing stm32f2xx_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f2xx_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f2xx_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32f2xx_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32f2xx_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32f2xx_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f2xx_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32f2xx_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f2xx_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32f2xx_it.o(.ARM.exidx.text.TIM1_UP_TIM10_IRQHandler), (8 bytes).
    Removing stm32f2xx_it.o(.ARM.exidx.text.TIM3_IRQHandler), (8 bytes).
    Removing stm32f2xx_it.o(.ARM.exidx.text.EXTI9_5_IRQHandler), (8 bytes).
    Removing stm32f2xx_hal_msp.o(.text), (0 bytes).
    Removing stm32f2xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f2xx_hal_msp.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing stm32f2xx_hal_msp.o(.text.HAL_TIM_Base_MspDeInit), (126 bytes).
    Removing stm32f2xx_hal_msp.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing test_board_control.o(.text), (0 bytes).
    Removing test_board_control.o(.ARM.exidx.text.sum_array), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_GetTypeName), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_GetStatusName), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_Init), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_BuildPacket), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_ValidatePacket), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_SendPacket), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_PowerOn), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.update_board_info), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_PowerOff), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_Reset), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_GetStatus), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_SetVoltage), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_SetCurrent), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_StartTest), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_StopTest), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_PowerOnAll), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_PowerOffAll), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_EmergencyStop), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_GetInfo), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.Display_Init), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.Display_SendCommand), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.Display_ShowMessage), (8 bytes).
    Removing test_board_control.o(.text.Display_SetPage), (42 bytes).
    Removing test_board_control.o(.ARM.exidx.text.Display_SetPage), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.Display_SetComponent), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.Display_UpdateStatus), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.Display_ShowBoardStatus), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_UpdateStatus), (8 bytes).
    Removing test_board_control.o(.ARM.exidx.text.TestBoard_ProcessHeartbeat), (8 bytes).
    Removing test_board_test.o(.text), (0 bytes).
    Removing test_board_test.o(.ARM.exidx.text.TestBoard_RunAllTests), (8 bytes).
    Removing test_board_test.o(.ARM.exidx.text.test_uart_communication), (8 bytes).
    Removing test_board_test.o(.ARM.exidx.text.test_packet_validation), (8 bytes).
    Removing test_board_test.o(.ARM.exidx.text.test_board_control_commands), (8 bytes).
    Removing test_board_test.o(.ARM.exidx.text.test_display_functions), (8 bytes).
    Removing test_board_test.o(.ARM.exidx.text.test_error_handling), (8 bytes).
    Removing test_board_test.o(.ARM.exidx.text.print_test_summary), (8 bytes).
    Removing test_board_test.o(.text.TestBoard_GetTestResult), (10 bytes).
    Removing test_board_test.o(.ARM.exidx.text.TestBoard_GetTestResult), (8 bytes).
    Removing test_board_test.o(.text.TestBoard_ResetTestResult), (20 bytes).
    Removing test_board_test.o(.ARM.exidx.text.TestBoard_ResetTestResult), (8 bytes).
    Removing test_board_test.o(.rodata..L__const.test_packet_validation.test_data), (3 bytes).
    Removing stm32f2xx_hal_tim.o(.text), (0 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_DeInit), (166 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Start), (234 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Stop), (70 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT), (82 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA), (374 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt), (42 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt), (22 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_DMAError), (154 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA), (92 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Init), (156 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_MspInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_DeInit), (166 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Start), (458 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_CCxChannelCmd), (54 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Stop), (262 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT), (572 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT), (374 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA), (986 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_DMADelayPulseCplt), (188 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt), (116 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA), (406 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Init), (156 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit), (166 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Start), (458 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Stop), (262 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT), (572 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT), (374 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA), (986 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA), (406 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Init), (156 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_MspInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_DeInit), (166 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Start), (566 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Stop), (234 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT), (678 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT), (346 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA), (1018 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_DMACaptureCplt), (212 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt), (116 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA), (376 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init), (144 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit), (128 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start), (230 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop), (224 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT), (254 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT), (248 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Init), (304 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit), (128 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Start), (334 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop), (338 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT), (382 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT), (386 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA), (896 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA), (418 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel), (164 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_OC1_SetConfig), (234 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_OC1_SetConfig), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_OC2_SetConfig), (244 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_OC3_SetConfig), (242 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_OC3_SetConfig), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_OC4_SetConfig), (164 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_OC4_SetConfig), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel), (304 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_TI1_SetConfig), (278 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_TI2_SetConfig), (108 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_TI2_SetConfig), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_TI3_SetConfig), (106 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_TI3_SetConfig), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_TI4_SetConfig), (108 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_TI4_SetConfig), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel), (322 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel), (394 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart), (62 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart), (736 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_DMATriggerCplt), (42 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt), (22 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop), (202 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart), (62 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart), (736 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop), (202 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_GenerateEvent), (92 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear), (384 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_ConfigInputStage), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_ITRx_SetConfig), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_TI2_ConfigInputStage), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input), (44 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro), (150 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig), (296 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT), (150 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue), (86 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_ErrorCallback), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_GetState), (14 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_GetState), (14 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_GetState), (14 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_GetState), (14 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState), (14 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState), (14 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel), (12 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_GetChannelState), (94 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState), (8 bytes).
    Removing stm32f2xx_hal_tim.o(.text.HAL_TIM_DMABurstState), (14 bytes).
    Removing stm32f2xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text), (0 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init), (280 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit), (128 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start), (334 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop), (102 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT), (346 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT), (114 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA), (428 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA), (110 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start), (402 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.TIM_CCxNChannelCmd), (54 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.TIM_CCxNChannelCmd), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop), (208 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT), (508 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT), (338 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA), (836 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt), (148 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN), (116 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA), (326 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start), (402 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop), (208 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT), (508 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT), (338 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA), (836 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA), (326 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start), (186 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop), (182 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT), (210 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT), (206 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent), (186 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT), (186 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA), (228 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt), (30 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt), (30 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime), (178 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig), (76 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState), (14 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState), (94 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState), (8 bytes).
    Removing stm32f2xx_hal.o(.text), (0 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_MspInit), (2 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_DeInit), (82 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_SetTickFreq), (106 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_GetTickFreq), (12 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_SuspendTick), (18 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_ResumeTick), (18 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_GetHalVersion), (10 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_GetREVID), (14 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_GetDEVID), (16 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode), (18 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode), (18 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (18 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (18 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (18 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (18 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_EnableCompensationCell), (14 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_DisableCompensationCell), (14 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_GetUIDw0), (12 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_GetUIDw1), (12 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1), (8 bytes).
    Removing stm32f2xx_hal.o(.text.HAL_GetUIDw2), (12 bytes).
    Removing stm32f2xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2), (8 bytes).
    Removing stm32f2xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32f2xx_hal_rcc.o(.text.HAL_RCC_DeInit), (472 bytes).
    Removing stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32f2xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (208 bytes).
    Removing stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32f2xx_hal_rcc.o(.text.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32f2xx_hal_rcc.o(.text.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS), (8 bytes).
    Removing stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq), (34 bytes).
    Removing stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq), (34 bytes).
    Removing stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq), (8 bytes).
    Removing stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (316 bytes).
    Removing stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (84 bytes).
    Removing stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32f2xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler), (40 bytes).
    Removing stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32f2xx_hal_rcc.o(.text.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f2xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32f2xx_hal_rcc_ex.o(.text), (0 bytes).
    Removing stm32f2xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig), (566 bytes).
    Removing stm32f2xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig), (8 bytes).
    Removing stm32f2xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig), (80 bytes).
    Removing stm32f2xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig), (8 bytes).
    Removing stm32f2xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S), (176 bytes).
    Removing stm32f2xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S), (8 bytes).
    Removing stm32f2xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S), (80 bytes).
    Removing stm32f2xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SetPriority), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.NVIC_EncodePriority), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ), (20 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.__NVIC_DisableIRQ), (56 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_DisableIRQ), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (4 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.__NVIC_SystemReset), (38 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.SysTick_Config), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.HAL_MPU_Disable), (34 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.HAL_MPU_Enable), (48 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.HAL_MPU_EnableRegion), (36 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.HAL_MPU_DisableRegion), (36 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion), (128 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (36 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.NVIC_DecodePriority), (118 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.NVIC_DecodePriority), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.__NVIC_GetPriority), (66 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_GetPriority), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (20 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.__NVIC_SetPendingIRQ), (48 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (20 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.__NVIC_GetPendingIRQ), (64 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (20 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.__NVIC_ClearPendingIRQ), (48 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.HAL_NVIC_GetActive), (20 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.__NVIC_GetActive), (64 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.__NVIC_GetActive), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (52 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f2xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.text), (0 bytes).
    Removing stm32f2xx_hal_flash.o(.text.HAL_FLASH_Program), (216 bytes).
    Removing stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.text.FLASH_WaitForLastOperation), (164 bytes).
    Removing stm32f2xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.text.FLASH_Program_Byte), (48 bytes).
    Removing stm32f2xx_hal_flash.o(.ARM.exidx.text.FLASH_Program_Byte), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.text.FLASH_Program_HalfWord), (52 bytes).
    Removing stm32f2xx_hal_flash.o(.ARM.exidx.text.FLASH_Program_HalfWord), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.text.FLASH_Program_Word), (48 bytes).
    Removing stm32f2xx_hal_flash.o(.ARM.exidx.text.FLASH_Program_Word), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.text.FLASH_Program_DoubleWord), (60 bytes).
    Removing stm32f2xx_hal_flash.o(.ARM.exidx.text.FLASH_Program_DoubleWord), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.text.HAL_FLASH_Program_IT), (138 bytes).
    Removing stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.text.HAL_FLASH_IRQHandler), (394 bytes).
    Removing stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.text.FLASH_SetErrorCode), (242 bytes).
    Removing stm32f2xx_hal_flash.o(.ARM.exidx.text.FLASH_SetErrorCode), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.text.HAL_FLASH_Unlock), (86 bytes).
    Removing stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.text.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock), (72 bytes).
    Removing stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.text.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.text.HAL_FLASH_OB_Launch), (28 bytes).
    Removing stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.text.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f2xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError), (8 bytes).
    Removing stm32f2xx_hal_flash.o(.bss.pFlash), (32 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.text), (0 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase), (270 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase), (8 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.text.FLASH_MassErase), (52 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_MassErase), (8 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.text.FLASH_Erase_Sector), (132 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector), (8 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.text.FLASH_FlushCaches), (162 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches), (8 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT), (138 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT), (8 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram), (232 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram), (8 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_EnableWRP), (60 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_EnableWRP), (8 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_DisableWRP), (60 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_DisableWRP), (8 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_RDP_LevelConfig), (58 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_RDP_LevelConfig), (8 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_UserConfig), (102 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_UserConfig), (8 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_BOR_LevelConfig), (38 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_BOR_LevelConfig), (8 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig), (48 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig), (8 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_GetWRP), (8 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_GetRDP), (74 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_GetRDP), (8 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_GetUser), (8 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.text.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_OB_GetBOR), (8 bytes).
    Removing stm32f2xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32f2xx_hal_pwr.o(.text.HAL_PWR_DeInit), (26 bytes).
    Removing stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32f2xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32f2xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32f2xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD), (210 bytes).
    Removing stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD), (8 bytes).
    Removing stm32f2xx_hal_pwr.o(.text.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD), (8 bytes).
    Removing stm32f2xx_hal_pwr.o(.text.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD), (8 bytes).
    Removing stm32f2xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (24 bytes).
    Removing stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32f2xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (24 bytes).
    Removing stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32f2xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (50 bytes).
    Removing stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32f2xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (86 bytes).
    Removing stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f2xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f2xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler), (42 bytes).
    Removing stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler), (8 bytes).
    Removing stm32f2xx_hal_pwr.o(.text.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback), (8 bytes).
    Removing stm32f2xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (18 bytes).
    Removing stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32f2xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (18 bytes).
    Removing stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32f2xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (18 bytes).
    Removing stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32f2xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (18 bytes).
    Removing stm32f2xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing stm32f2xx_hal_pwr_ex.o(.text), (0 bytes).
    Removing stm32f2xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg), (92 bytes).
    Removing stm32f2xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg), (8 bytes).
    Removing stm32f2xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg), (90 bytes).
    Removing stm32f2xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg), (8 bytes).
    Removing stm32f2xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f2xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown), (8 bytes).
    Removing stm32f2xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f2xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown), (8 bytes).
    Removing stm32f2xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32f2xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32f2xx_hal_gpio.o(.text.HAL_GPIO_DeInit), (566 bytes).
    Removing stm32f2xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32f2xx_hal_gpio.o(.text.HAL_GPIO_ReadPin), (46 bytes).
    Removing stm32f2xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32f2xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32f2xx_hal_gpio.o(.text.HAL_GPIO_TogglePin), (38 bytes).
    Removing stm32f2xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f2xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (86 bytes).
    Removing stm32f2xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32f2xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32f2xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback), (10 bytes).
    Removing stm32f2xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing stm32f2xx_hal_dma.o(.text), (0 bytes).
    Removing stm32f2xx_hal_dma.o(.text.HAL_DMA_Init), (366 bytes).
    Removing stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32f2xx_hal_dma.o(.text.DMA_CheckFifoParam), (240 bytes).
    Removing stm32f2xx_hal_dma.o(.ARM.exidx.text.DMA_CheckFifoParam), (8 bytes).
    Removing stm32f2xx_hal_dma.o(.text.DMA_CalcBaseAndBitshift), (86 bytes).
    Removing stm32f2xx_hal_dma.o(.ARM.exidx.text.DMA_CalcBaseAndBitshift), (8 bytes).
    Removing stm32f2xx_hal_dma.o(.text.HAL_DMA_DeInit), (180 bytes).
    Removing stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32f2xx_hal_dma.o(.text.HAL_DMA_Start), (142 bytes).
    Removing stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32f2xx_hal_dma.o(.text.DMA_SetConfig), (80 bytes).
    Removing stm32f2xx_hal_dma.o(.ARM.exidx.text.DMA_SetConfig), (8 bytes).
    Removing stm32f2xx_hal_dma.o(.text.HAL_DMA_Start_IT), (194 bytes).
    Removing stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32f2xx_hal_dma.o(.text.HAL_DMA_Abort), (236 bytes).
    Removing stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32f2xx_hal_dma.o(.text.HAL_DMA_Abort_IT), (68 bytes).
    Removing stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32f2xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (506 bytes).
    Removing stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32f2xx_hal_dma.o(.text.HAL_DMA_IRQHandler), (798 bytes).
    Removing stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32f2xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (182 bytes).
    Removing stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32f2xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (210 bytes).
    Removing stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32f2xx_hal_dma.o(.text.HAL_DMA_GetState), (14 bytes).
    Removing stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32f2xx_hal_dma.o(.text.HAL_DMA_GetError), (12 bytes).
    Removing stm32f2xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32f2xx_hal_dma.o(.rodata.DMA_CalcBaseAndBitshift.flagBitshiftOffset), (8 bytes).
    Removing stm32f2xx_hal_dma_ex.o(.text), (0 bytes).
    Removing stm32f2xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart), (176 bytes).
    Removing stm32f2xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart), (8 bytes).
    Removing stm32f2xx_hal_dma_ex.o(.text.DMA_MultiBufferSetConfig), (68 bytes).
    Removing stm32f2xx_hal_dma_ex.o(.ARM.exidx.text.DMA_MultiBufferSetConfig), (8 bytes).
    Removing stm32f2xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT), (8244 bytes).
    Removing stm32f2xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT), (8 bytes).
    Removing stm32f2xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory), (44 bytes).
    Removing stm32f2xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory), (8 bytes).
    Removing stm32f2xx_hal_exti.o(.text), (0 bytes).
    Removing stm32f2xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine), (358 bytes).
    Removing stm32f2xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine), (8 bytes).
    Removing stm32f2xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine), (268 bytes).
    Removing stm32f2xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine), (8 bytes).
    Removing stm32f2xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine), (196 bytes).
    Removing stm32f2xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine), (8 bytes).
    Removing stm32f2xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback), (48 bytes).
    Removing stm32f2xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback), (8 bytes).
    Removing stm32f2xx_hal_exti.o(.text.HAL_EXTI_GetHandle), (42 bytes).
    Removing stm32f2xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle), (8 bytes).
    Removing stm32f2xx_hal_exti.o(.text.HAL_EXTI_IRQHandler), (76 bytes).
    Removing stm32f2xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler), (8 bytes).
    Removing stm32f2xx_hal_exti.o(.text.HAL_EXTI_GetPending), (50 bytes).
    Removing stm32f2xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending), (8 bytes).
    Removing stm32f2xx_hal_exti.o(.text.HAL_EXTI_ClearPending), (36 bytes).
    Removing stm32f2xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending), (8 bytes).
    Removing stm32f2xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI), (34 bytes).
    Removing stm32f2xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI), (8 bytes).
    Removing system_stm32f2xx.o(.text), (0 bytes).
    Removing system_stm32f2xx.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f2xx.o(.text.SystemCoreClockUpdate), (302 bytes).
    Removing system_stm32f2xx.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing system_stm32f2xx.o(.rodata.APBPrescTable), (8 bytes).

724 unused section(s) (total 57595 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc_h.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    startup_stm32f205xx.s                    0x00000000   Number         0  startup_stm32f205xx.o ABSOLUTE
    stm32f2xx_hal.c                          0x00000000   Number         0  stm32f2xx_hal.o ABSOLUTE
    stm32f2xx_hal_cortex.c                   0x00000000   Number         0  stm32f2xx_hal_cortex.o ABSOLUTE
    stm32f2xx_hal_dma.c                      0x00000000   Number         0  stm32f2xx_hal_dma.o ABSOLUTE
    stm32f2xx_hal_dma_ex.c                   0x00000000   Number         0  stm32f2xx_hal_dma_ex.o ABSOLUTE
    stm32f2xx_hal_exti.c                     0x00000000   Number         0  stm32f2xx_hal_exti.o ABSOLUTE
    stm32f2xx_hal_flash.c                    0x00000000   Number         0  stm32f2xx_hal_flash.o ABSOLUTE
    stm32f2xx_hal_flash_ex.c                 0x00000000   Number         0  stm32f2xx_hal_flash_ex.o ABSOLUTE
    stm32f2xx_hal_gpio.c                     0x00000000   Number         0  stm32f2xx_hal_gpio.o ABSOLUTE
    stm32f2xx_hal_msp.c                      0x00000000   Number         0  stm32f2xx_hal_msp.o ABSOLUTE
    stm32f2xx_hal_pwr.c                      0x00000000   Number         0  stm32f2xx_hal_pwr.o ABSOLUTE
    stm32f2xx_hal_pwr_ex.c                   0x00000000   Number         0  stm32f2xx_hal_pwr_ex.o ABSOLUTE
    stm32f2xx_hal_rcc.c                      0x00000000   Number         0  stm32f2xx_hal_rcc.o ABSOLUTE
    stm32f2xx_hal_rcc_ex.c                   0x00000000   Number         0  stm32f2xx_hal_rcc_ex.o ABSOLUTE
    stm32f2xx_hal_tim.c                      0x00000000   Number         0  stm32f2xx_hal_tim.o ABSOLUTE
    stm32f2xx_hal_tim_ex.c                   0x00000000   Number         0  stm32f2xx_hal_tim_ex.o ABSOLUTE
    stm32f2xx_it.c                           0x00000000   Number         0  stm32f2xx_it.o ABSOLUTE
    system_stm32f2xx.c                       0x00000000   Number         0  system_stm32f2xx.o ABSOLUTE
    test_board_control.c                     0x00000000   Number         0  test_board_control.o ABSOLUTE
    test_board_test.c                        0x00000000   Number         0  test_board_test.o ABSOLUTE
    RESET                                    0x08000000   Section      388  startup_stm32f205xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000184   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000184   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000188   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x0800018c   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x0800018c   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x0800018c   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x08000194   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x08000194   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000194   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000194   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000198   Section       36  startup_stm32f205xx.o(.text)
    .text                                    0x080001bc   Section        0  uldiv.o(.text)
    .text                                    0x0800021e   Section        0  memcpya.o(.text)
    .text                                    0x08000242   Section        0  memseta.o(.text)
    .text                                    0x08000266   Section        0  memcmp.o(.text)
    .text                                    0x08000280   Section        0  strcpy.o(.text)
    .text                                    0x08000292   Section        0  uidiv.o(.text)
    .text                                    0x080002be   Section        0  llshl.o(.text)
    .text                                    0x080002dc   Section        0  llushr.o(.text)
    .text                                    0x080002fc   Section        0  iusefp.o(.text)
    .text                                    0x080002fc   Section        0  dadd.o(.text)
    .text                                    0x0800044a   Section        0  dmul.o(.text)
    .text                                    0x0800052e   Section        0  ddiv.o(.text)
    .text                                    0x0800060c   Section        0  dfixul.o(.text)
    .text                                    0x0800063c   Section       48  cdrcmple.o(.text)
    .text                                    0x0800066c   Section       48  init.o(.text)
    .text                                    0x0800069c   Section        0  semi.o(.text)
    .text                                    0x0800069c   Section        0  llsshr.o(.text)
    .text                                    0x080006c0   Section        0  iusesemip.o(.text)
    .text                                    0x080006c0   Section        0  depilogue.o(.text)
    [Anonymous Symbol]                       0x0800077c   Section        0  stm32f2xx_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x08000780   Section        0  stm32f2xx_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x08000784   Section        0  test_board_control.o(.text.Display_Init)
    [Anonymous Symbol]                       0x080007e4   Section        0  test_board_control.o(.text.Display_SendCommand)
    [Anonymous Symbol]                       0x0800081c   Section        0  test_board_control.o(.text.Display_SetComponent)
    [Anonymous Symbol]                       0x08000864   Section        0  test_board_control.o(.text.Display_ShowBoardStatus)
    [Anonymous Symbol]                       0x080008a8   Section        0  test_board_control.o(.text.Display_ShowMessage)
    [Anonymous Symbol]                       0x080008d4   Section        0  test_board_control.o(.text.Display_UpdateStatus)
    [Anonymous Symbol]                       0x080009e8   Section        0  stm32f2xx_it.o(.text.EXTI9_5_IRQHandler)
    [Anonymous Symbol]                       0x080009f4   Section        0  main.o(.text.Error_Handler)
    [Anonymous Symbol]                       0x08000a04   Section        0  stm32f2xx_hal.o(.text.HAL_Delay)
    [Anonymous Symbol]                       0x08000a48   Section        0  main.o(.text.HAL_GPIO_EXTI_Callback)
    [Anonymous Symbol]                       0x08000a60   Section        0  stm32f2xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler)
    [Anonymous Symbol]                       0x08000a98   Section        0  stm32f2xx_hal_gpio.o(.text.HAL_GPIO_Init)
    [Anonymous Symbol]                       0x08000e64   Section        0  stm32f2xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    [Anonymous Symbol]                       0x08000e94   Section        0  stm32f2xx_hal.o(.text.HAL_GetTick)
    [Anonymous Symbol]                       0x08000ea0   Section        0  stm32f2xx_hal.o(.text.HAL_IncTick)
    [Anonymous Symbol]                       0x08000ebc   Section        0  stm32f2xx_hal.o(.text.HAL_Init)
    [Anonymous Symbol]                       0x08000ef4   Section        0  stm32f2xx_hal.o(.text.HAL_InitTick)
    [Anonymous Symbol]                       0x08000f64   Section        0  stm32f2xx_hal_msp.o(.text.HAL_MspInit)
    [Anonymous Symbol]                       0x08000fb0   Section        0  stm32f2xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x08000fc4   Section        0  stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    [Anonymous Symbol]                       0x08000ff8   Section        0  stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x08001008   Section        0  stm32f2xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    [Anonymous Symbol]                       0x08001260   Section        0  stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    [Anonymous Symbol]                       0x08001348   Section        0  stm32f2xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    [Anonymous Symbol]                       0x08001974   Section        0  stm32f2xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    [Anonymous Symbol]                       0x08001984   Section        0  stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback)
    [Anonymous Symbol]                       0x0800198c   Section        0  stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback)
    [Anonymous Symbol]                       0x08001994   Section        0  stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    [Anonymous Symbol]                       0x08001ab8   Section        0  stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    [Anonymous Symbol]                       0x08001b54   Section        0  stm32f2xx_hal_msp.o(.text.HAL_TIM_Base_MspInit)
    [Anonymous Symbol]                       0x08001c20   Section        0  stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT)
    [Anonymous Symbol]                       0x08001d18   Section        0  stm32f2xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    [Anonymous Symbol]                       0x08001e9c   Section        0  stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback)
    [Anonymous Symbol]                       0x08001ea4   Section        0  stm32f2xx_hal_tim.o(.text.HAL_TIM_IRQHandler)
    [Anonymous Symbol]                       0x080020c0   Section        0  stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback)
    [Anonymous Symbol]                       0x080020c8   Section        0  stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback)
    [Anonymous Symbol]                       0x080020d0   Section        0  main.o(.text.HAL_TIM_PeriodElapsedCallback)
    [Anonymous Symbol]                       0x080022c4   Section        0  stm32f2xx_hal_tim.o(.text.HAL_TIM_TriggerCallback)
    [Anonymous Symbol]                       0x080022cc   Section        0  stm32f2xx_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x080022d0   Section        0  main.o(.text.IOUART_Init)
    [Anonymous Symbol]                       0x08002414   Section        0  main.o(.text.IOUART_PutChar)
    [Anonymous Symbol]                       0x08002494   Section        0  main.o(.text.IOUART_SendByte)
    [Anonymous Symbol]                       0x08002520   Section        0  main.o(.text.IOUART_SendString)
    MX_TIM1_Init                             0x0800255d   Thumb Code   134  main.o(.text.MX_TIM1_Init)
    [Anonymous Symbol]                       0x0800255c   Section        0  main.o(.text.MX_TIM1_Init)
    MX_TIM2_Init                             0x080025e5   Thumb Code   130  main.o(.text.MX_TIM2_Init)
    [Anonymous Symbol]                       0x080025e4   Section        0  main.o(.text.MX_TIM2_Init)
    MX_TIM3_Init                             0x08002669   Thumb Code   132  main.o(.text.MX_TIM3_Init)
    [Anonymous Symbol]                       0x08002668   Section        0  main.o(.text.MX_TIM3_Init)
    [Anonymous Symbol]                       0x080026ec   Section        0  stm32f2xx_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x080026f0   Section        0  stm32f2xx_it.o(.text.NMI_Handler)
    NVIC_EncodePriority                      0x080026f5   Thumb Code   108  stm32f2xx_hal_cortex.o(.text.NVIC_EncodePriority)
    [Anonymous Symbol]                       0x080026f4   Section        0  stm32f2xx_hal_cortex.o(.text.NVIC_EncodePriority)
    [Anonymous Symbol]                       0x08002760   Section        0  stm32f2xx_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x08002764   Section        0  stm32f2xx_it.o(.text.SVC_Handler)
    SysTick_Config                           0x08002769   Thumb Code    82  stm32f2xx_hal_cortex.o(.text.SysTick_Config)
    [Anonymous Symbol]                       0x08002768   Section        0  stm32f2xx_hal_cortex.o(.text.SysTick_Config)
    [Anonymous Symbol]                       0x080027bc   Section        0  stm32f2xx_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x080027c4   Section        0  main.o(.text.SystemClock_Config)
    [Anonymous Symbol]                       0x0800283c   Section        0  system_stm32f2xx.o(.text.SystemInit)
    [Anonymous Symbol]                       0x08002840   Section        0  stm32f2xx_it.o(.text.TIM1_UP_TIM10_IRQHandler)
    [Anonymous Symbol]                       0x08002850   Section        0  stm32f2xx_it.o(.text.TIM3_IRQHandler)
    [Anonymous Symbol]                       0x08002860   Section        0  stm32f2xx_hal_tim.o(.text.TIM_Base_SetConfig)
    [Anonymous Symbol]                       0x08002a10   Section        0  stm32f2xx_hal_tim.o(.text.TIM_ETR_SetConfig)
    TIM_ITRx_SetConfig                       0x08002a45   Thumb Code    42  stm32f2xx_hal_tim.o(.text.TIM_ITRx_SetConfig)
    [Anonymous Symbol]                       0x08002a44   Section        0  stm32f2xx_hal_tim.o(.text.TIM_ITRx_SetConfig)
    TIM_TI1_ConfigInputStage                 0x08002a71   Thumb Code    80  stm32f2xx_hal_tim.o(.text.TIM_TI1_ConfigInputStage)
    [Anonymous Symbol]                       0x08002a70   Section        0  stm32f2xx_hal_tim.o(.text.TIM_TI1_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08002ac1   Thumb Code    82  stm32f2xx_hal_tim.o(.text.TIM_TI2_ConfigInputStage)
    [Anonymous Symbol]                       0x08002ac0   Section        0  stm32f2xx_hal_tim.o(.text.TIM_TI2_ConfigInputStage)
    [Anonymous Symbol]                       0x08002b14   Section        0  test_board_control.o(.text.TestBoard_BuildPacket)
    [Anonymous Symbol]                       0x08002bc0   Section        0  test_board_control.o(.text.TestBoard_EmergencyStop)
    [Anonymous Symbol]                       0x08002bfc   Section        0  test_board_control.o(.text.TestBoard_GetInfo)
    [Anonymous Symbol]                       0x08002c30   Section        0  test_board_control.o(.text.TestBoard_GetStatus)
    [Anonymous Symbol]                       0x08002c6c   Section        0  test_board_control.o(.text.TestBoard_GetStatusName)
    [Anonymous Symbol]                       0x08002cd8   Section        0  test_board_control.o(.text.TestBoard_GetTypeName)
    [Anonymous Symbol]                       0x08002d6c   Section        0  test_board_control.o(.text.TestBoard_Init)
    [Anonymous Symbol]                       0x08002e34   Section        0  test_board_control.o(.text.TestBoard_PowerOff)
    [Anonymous Symbol]                       0x08002ea8   Section        0  test_board_control.o(.text.TestBoard_PowerOffAll)
    [Anonymous Symbol]                       0x08002ef8   Section        0  test_board_control.o(.text.TestBoard_PowerOn)
    [Anonymous Symbol]                       0x08002f6c   Section        0  test_board_control.o(.text.TestBoard_PowerOnAll)
    [Anonymous Symbol]                       0x08002fbc   Section        0  test_board_control.o(.text.TestBoard_ProcessHeartbeat)
    [Anonymous Symbol]                       0x08003054   Section        0  test_board_control.o(.text.TestBoard_Reset)
    [Anonymous Symbol]                       0x080030c8   Section        0  test_board_test.o(.text.TestBoard_RunAllTests)
    [Anonymous Symbol]                       0x08003140   Section        0  test_board_control.o(.text.TestBoard_SendPacket)
    [Anonymous Symbol]                       0x080031c0   Section        0  test_board_control.o(.text.TestBoard_SetCurrent)
    [Anonymous Symbol]                       0x0800325c   Section        0  test_board_control.o(.text.TestBoard_SetVoltage)
    [Anonymous Symbol]                       0x080032f8   Section        0  test_board_control.o(.text.TestBoard_StartTest)
    [Anonymous Symbol]                       0x08003384   Section        0  test_board_control.o(.text.TestBoard_StopTest)
    [Anonymous Symbol]                       0x080033f8   Section        0  test_board_control.o(.text.TestBoard_UpdateStatus)
    [Anonymous Symbol]                       0x08003528   Section        0  test_board_control.o(.text.TestBoard_ValidatePacket)
    [Anonymous Symbol]                       0x080035c8   Section        0  stm32f2xx_it.o(.text.UsageFault_Handler)
    __NVIC_EnableIRQ                         0x080035cd   Thumb Code    48  stm32f2xx_hal_cortex.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x080035cc   Section        0  stm32f2xx_hal_cortex.o(.text.__NVIC_EnableIRQ)
    __NVIC_GetPriorityGrouping               0x080035fd   Thumb Code    16  stm32f2xx_hal_cortex.o(.text.__NVIC_GetPriorityGrouping)
    [Anonymous Symbol]                       0x080035fc   Section        0  stm32f2xx_hal_cortex.o(.text.__NVIC_GetPriorityGrouping)
    __NVIC_SetPriority                       0x0800360d   Thumb Code    66  stm32f2xx_hal_cortex.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x0800360c   Section        0  stm32f2xx_hal_cortex.o(.text.__NVIC_SetPriority)
    __NVIC_SetPriorityGrouping               0x08003651   Thumb Code    60  stm32f2xx_hal_cortex.o(.text.__NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x08003650   Section        0  stm32f2xx_hal_cortex.o(.text.__NVIC_SetPriorityGrouping)
    iouart_TXD                               0x0800368d   Thumb Code    88  main.o(.text.iouart_TXD)
    [Anonymous Symbol]                       0x0800368c   Section        0  main.o(.text.iouart_TXD)
    [Anonymous Symbol]                       0x080036e4   Section        0  main.o(.text.main)
    print_test_summary                       0x08003815   Thumb Code     2  test_board_test.o(.text.print_test_summary)
    [Anonymous Symbol]                       0x08003814   Section        0  test_board_test.o(.text.print_test_summary)
    [Anonymous Symbol]                       0x08003818   Section        0  test_board_control.o(.text.sum_array)
    test_board_control_commands              0x0800385d   Thumb Code  1520  test_board_test.o(.text.test_board_control_commands)
    [Anonymous Symbol]                       0x0800385c   Section        0  test_board_test.o(.text.test_board_control_commands)
    test_display_functions                   0x08003e4d   Thumb Code   278  test_board_test.o(.text.test_display_functions)
    [Anonymous Symbol]                       0x08003e4c   Section        0  test_board_test.o(.text.test_display_functions)
    test_error_handling                      0x08003f65   Thumb Code   532  test_board_test.o(.text.test_error_handling)
    [Anonymous Symbol]                       0x08003f64   Section        0  test_board_test.o(.text.test_error_handling)
    test_packet_validation                   0x08004179   Thumb Code  1022  test_board_test.o(.text.test_packet_validation)
    [Anonymous Symbol]                       0x08004178   Section        0  test_board_test.o(.text.test_packet_validation)
    test_uart_communication                  0x08004579   Thumb Code   128  test_board_test.o(.text.test_uart_communication)
    [Anonymous Symbol]                       0x08004578   Section        0  test_board_test.o(.text.test_uart_communication)
    update_board_info                        0x080045f9   Thumb Code   142  test_board_control.o(.text.update_board_info)
    [Anonymous Symbol]                       0x080045f8   Section        0  test_board_control.o(.text.update_board_info)
    i.__0printf                              0x08004688   Section        0  printfa.o(i.__0printf)
    i.__0snprintf                            0x080046a8   Section        0  printfa.o(i.__0snprintf)
    i.__scatterload_copy                     0x080046dc   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080046ea   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080046ec   Section       14  handlers.o(i.__scatterload_zeroinit)
    _fp_digits                               0x080046fd   Thumb Code   366  printfa.o(i._fp_digits)
    i._fp_digits                             0x080046fc   Section        0  printfa.o(i._fp_digits)
    _printf_core                             0x08004881   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_core                           0x08004880   Section        0  printfa.o(i._printf_core)
    _printf_post_padding                     0x08004f5d   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_post_padding                   0x08004f5c   Section        0  printfa.o(i._printf_post_padding)
    _printf_pre_padding                      0x08004f81   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x08004f80   Section        0  printfa.o(i._printf_pre_padding)
    _snputc                                  0x08004faf   Thumb Code    22  printfa.o(i._snputc)
    i._snputc                                0x08004fae   Section        0  printfa.o(i._snputc)
    i.fputc                                  0x08004fc4   Section        0  fputc.o(i.fputc)
    .L.str.3                                 0x08004fe6   Data          19  main.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08004fe6   Section        0  main.o(.rodata.str1.1)
    .L.str.2                                 0x08004ff9   Data          18  main.o(.rodata.str1.1)
    .L.str                                   0x0800500b   Data          21  main.o(.rodata.str1.1)
    .L.str.1                                 0x08005020   Data          24  main.o(.rodata.str1.1)
    .L.str.4                                 0x08005038   Data          20  main.o(.rodata.str1.1)
    .L.str.18                                0x0800504c   Data          11  test_board_control.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800504c   Section        0  test_board_control.o(.rodata.str1.1)
    .L.str.16                                0x08005057   Data          15  test_board_control.o(.rodata.str1.1)
    .L.str.14                                0x08005066   Data           7  test_board_control.o(.rodata.str1.1)
    .L.str.26                                0x0800506d   Data           4  test_board_control.o(.rodata.str1.1)
    .L.str.15                                0x08005071   Data           7  test_board_control.o(.rodata.str1.1)
    .L.str.20                                0x08005078   Data           3  test_board_control.o(.rodata.str1.1)
    .L.str.4                                 0x0800507b   Data           5  test_board_control.o(.rodata.str1.1)
    .L.str.5                                 0x08005080   Data           5  test_board_control.o(.rodata.str1.1)
    .L.str.1                                 0x08005085   Data           4  test_board_control.o(.rodata.str1.1)
    .L.str.6                                 0x08005089   Data           5  test_board_control.o(.rodata.str1.1)
    .L.str.2                                 0x0800508e   Data           4  test_board_control.o(.rodata.str1.1)
    .L.str.7                                 0x08005092   Data           5  test_board_control.o(.rodata.str1.1)
    .L.str.3                                 0x08005097   Data           4  test_board_control.o(.rodata.str1.1)
    .L.str.25                                0x0800509b   Data           3  test_board_control.o(.rodata.str1.1)
    .L.str.9                                 0x0800509e   Data           8  test_board_control.o(.rodata.str1.1)
    .L.str.13                                0x080050a6   Data          12  test_board_control.o(.rodata.str1.1)
    .L.str.8                                 0x080050b2   Data           8  test_board_control.o(.rodata.str1.1)
    .L.str.27                                0x080050ba   Data           8  test_board_control.o(.rodata.str1.1)
    .L.str.17                                0x080050bc   Data           8  test_board_control.o(.rodata.str1.1)
    .L.str.24                                0x080050c2   Data          22  test_board_control.o(.rodata.str1.1)
    .L.str.22                                0x080050e0   Data           4  test_board_control.o(.rodata.str1.1)
    .L.str.19                                0x080050e4   Data          18  test_board_control.o(.rodata.str1.1)
    .L.str.23                                0x080050f6   Data           7  test_board_control.o(.rodata.str1.1)
    .L.str.21                                0x080050fd   Data           4  test_board_control.o(.rodata.str1.1)
    .L.str.6                                 0x08005101   Data          20  test_board_test.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08005101   Section        0  test_board_test.o(.rodata.str1.1)
    .L.str.3                                 0x08005115   Data          20  test_board_test.o(.rodata.str1.1)
    .L.str.32                                0x08005129   Data          33  test_board_test.o(.rodata.str1.1)
    .L.str.28                                0x0800514a   Data          27  test_board_test.o(.rodata.str1.1)
    .L.str                                   0x08005165   Data          17  test_board_test.o(.rodata.str1.1)
    .L.str.13                                0x08005176   Data          39  test_board_test.o(.rodata.str1.1)
    .L.str.29                                0x0800519d   Data          15  test_board_test.o(.rodata.str1.1)
    .L.str.10                                0x080051ac   Data          34  test_board_test.o(.rodata.str1.1)
    .L.str.16                                0x080051ce   Data          29  test_board_test.o(.rodata.str1.1)
    .L.str                                   0x080051e7   Data           4  test_board_test.o(.rodata.str1.1)
    .L.str.30                                0x080051eb   Data          15  test_board_test.o(.rodata.str1.1)
    .L.str.12                                0x080051fa   Data          24  test_board_test.o(.rodata.str1.1)
    .L.str.18                                0x08005212   Data          28  test_board_test.o(.rodata.str1.1)
    .L.str.10                                0x08005229   Data           5  test_board_test.o(.rodata.str1.1)
    .L.str.9                                 0x0800522e   Data          35  test_board_test.o(.rodata.str1.1)
    .L.str.8                                 0x08005251   Data          39  test_board_test.o(.rodata.str1.1)
    .L.str.22                                0x08005278   Data          31  test_board_test.o(.rodata.str1.1)
    .L.str.11                                0x0800528f   Data           8  test_board_test.o(.rodata.str1.1)
    .L.str.15                                0x08005297   Data          13  test_board_test.o(.rodata.str1.1)
    .L.str.11                                0x080052a4   Data          31  test_board_test.o(.rodata.str1.1)
    .L.str.35                                0x080052c3   Data          35  test_board_test.o(.rodata.str1.1)
    .L.str.36                                0x080052c9   Data          29  test_board_test.o(.rodata.str1.1)
    .L.str.12                                0x080052e0   Data           6  test_board_test.o(.rodata.str1.1)
    .L.str.1                                 0x080052e6   Data          18  test_board_test.o(.rodata.str1.1)
    .L.str.31                                0x080052f8   Data           8  test_board_test.o(.rodata.str1.1)
    .L.str.14                                0x08005300   Data          24  test_board_test.o(.rodata.str1.1)
    .L.str.33                                0x08005318   Data          30  test_board_test.o(.rodata.str1.1)
    .L.str.34                                0x08005336   Data          31  test_board_test.o(.rodata.str1.1)
    .L.str.5                                 0x08005355   Data          21  test_board_test.o(.rodata.str1.1)
    .L.str.19                                0x0800536a   Data          23  test_board_test.o(.rodata.str1.1)
    .L.str.7                                 0x08005381   Data          24  test_board_test.o(.rodata.str1.1)
    .L.str.25                                0x08005399   Data          25  test_board_test.o(.rodata.str1.1)
    .L.str.27                                0x080053b2   Data          29  test_board_test.o(.rodata.str1.1)
    .L.str.26                                0x080053cf   Data          28  test_board_test.o(.rodata.str1.1)
    .L.str.17                                0x080053eb   Data          24  test_board_test.o(.rodata.str1.1)
    .L.str.24                                0x08005403   Data          21  test_board_test.o(.rodata.str1.1)
    .L.str.20                                0x08005418   Data          23  test_board_test.o(.rodata.str1.1)
    .L.str.23                                0x0800542f   Data          25  test_board_test.o(.rodata.str1.1)
    .L.str.21                                0x08005448   Data          26  test_board_test.o(.rodata.str1.1)
    .L.str.4                                 0x0800545d   Data           5  test_board_test.o(.rodata.str1.1)
    .L.str.2                                 0x08005462   Data          12  test_board_test.o(.rodata.str1.1)
    .data                                    0x20000000   Section        4  stdout.o(.data)
    HAL_TIM_PeriodElapsedCallback.global_time_us 0x200000a0   Data           4  main.o(.bss.HAL_TIM_PeriodElapsedCallback.global_time_us)
    [Anonymous Symbol]                       0x200000a0   Section        0  main.o(.bss.HAL_TIM_PeriodElapsedCallback.global_time_us)
    TestBoard_ProcessHeartbeat.last_heartbeat_time 0x200000a4   Data           4  test_board_control.o(.bss.TestBoard_ProcessHeartbeat.last_heartbeat_time)
    [Anonymous Symbol]                       0x200000a4   Section        0  test_board_control.o(.bss.TestBoard_ProcessHeartbeat.last_heartbeat_time)
    board_info                               0x200000a8   Data         192  test_board_control.o(.bss.board_info)
    [Anonymous Symbol]                       0x200000a8   Section        0  test_board_control.o(.bss.board_info)
    display_info                             0x20000168   Data         140  test_board_control.o(.bss.display_info)
    [Anonymous Symbol]                       0x20000168   Section        0  test_board_control.o(.bss.display_info)
    main.last_test_time                      0x200007b8   Data           4  main.o(.bss.main.last_test_time)
    [Anonymous Symbol]                       0x200007b8   Section        0  main.o(.bss.main.last_test_time)
    main.test_executed                       0x200007bc   Data           1  main.o(.bss.main.test_executed)
    [Anonymous Symbol]                       0x200007bc   Section        0  main.o(.bss.main.test_executed)
    test_result                              0x200007c0   Data          16  test_board_test.o(.bss.test_result)
    [Anonymous Symbol]                       0x200007c0   Section        0  test_board_test.o(.bss.test_result)
    STACK                                    0x200007d8   Section     1024  startup_stm32f205xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000184   Number         0  startup_stm32f205xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f205xx.o(RESET)
    __Vectors_End                            0x08000184   Data           0  startup_stm32f205xx.o(RESET)
    __main                                   0x08000185   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000185   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000189   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x0800018d   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x0800018d   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x0800018d   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000195   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000195   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000199   Thumb Code     8  startup_stm32f205xx.o(.text)
    ADC_IRQHandler                           0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    EXTI0_IRQHandler                         0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    EXTI1_IRQHandler                         0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    EXTI2_IRQHandler                         0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    EXTI3_IRQHandler                         0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    EXTI4_IRQHandler                         0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    FLASH_IRQHandler                         0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    FSMC_IRQHandler                          0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    OTG_FS_IRQHandler                        0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    OTG_HS_IRQHandler                        0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    PVD_IRQHandler                           0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    RCC_IRQHandler                           0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    SDIO_IRQHandler                          0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    SPI1_IRQHandler                          0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    SPI2_IRQHandler                          0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    SPI3_IRQHandler                          0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    TIM2_IRQHandler                          0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    TIM4_IRQHandler                          0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    TIM5_IRQHandler                          0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    TIM7_IRQHandler                          0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    UART4_IRQHandler                         0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    UART5_IRQHandler                         0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    USART1_IRQHandler                        0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    USART2_IRQHandler                        0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    USART3_IRQHandler                        0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    USART6_IRQHandler                        0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    WWDG_IRQHandler                          0x080001b3   Thumb Code     0  startup_stm32f205xx.o(.text)
    __aeabi_uldivmod                         0x080001bd   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x0800021f   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x0800021f   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x0800021f   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x08000243   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000243   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000243   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000251   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000251   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000251   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000255   Thumb Code    18  memseta.o(.text)
    memcmp                                   0x08000267   Thumb Code    26  memcmp.o(.text)
    strcpy                                   0x08000281   Thumb Code    18  strcpy.o(.text)
    __aeabi_uidiv                            0x08000293   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000293   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x080002bf   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080002bf   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080002dd   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080002dd   Thumb Code     0  llushr.o(.text)
    __I$use$fp                               0x080002fd   Thumb Code     0  iusefp.o(.text)
    __aeabi_dadd                             0x080002fd   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x0800043f   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000445   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x0800044b   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x0800052f   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x0800060d   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x0800063d   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x0800066d   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x0800066d   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x0800069d   Thumb Code    36  llsshr.o(.text)
    __semihosting_library_function           0x0800069d   Thumb Code     0  semi.o(.text)
    _ll_sshift_r                             0x0800069d   Thumb Code     0  llsshr.o(.text)
    __I$use$semihosting$fputc                0x080006c1   Thumb Code     0  iusesemip.o(.text)
    _double_round                            0x080006c1   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080006df   Thumb Code   156  depilogue.o(.text)
    BusFault_Handler                         0x0800077d   Thumb Code     4  stm32f2xx_it.o(.text.BusFault_Handler)
    DebugMon_Handler                         0x08000781   Thumb Code     2  stm32f2xx_it.o(.text.DebugMon_Handler)
    Display_Init                             0x08000785   Thumb Code    94  test_board_control.o(.text.Display_Init)
    Display_SendCommand                      0x080007e5   Thumb Code    56  test_board_control.o(.text.Display_SendCommand)
    Display_SetComponent                     0x0800081d   Thumb Code    72  test_board_control.o(.text.Display_SetComponent)
    Display_ShowBoardStatus                  0x08000865   Thumb Code    66  test_board_control.o(.text.Display_ShowBoardStatus)
    Display_ShowMessage                      0x080008a9   Thumb Code    42  test_board_control.o(.text.Display_ShowMessage)
    Display_UpdateStatus                     0x080008d5   Thumb Code   274  test_board_control.o(.text.Display_UpdateStatus)
    EXTI9_5_IRQHandler                       0x080009e9   Thumb Code    12  stm32f2xx_it.o(.text.EXTI9_5_IRQHandler)
    Error_Handler                            0x080009f5   Thumb Code    14  main.o(.text.Error_Handler)
    HAL_Delay                                0x08000a05   Thumb Code    66  stm32f2xx_hal.o(.text.HAL_Delay)
    HAL_GPIO_EXTI_Callback                   0x08000a49   Thumb Code    24  main.o(.text.HAL_GPIO_EXTI_Callback)
    HAL_GPIO_EXTI_IRQHandler                 0x08000a61   Thumb Code    56  stm32f2xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler)
    HAL_GPIO_Init                            0x08000a99   Thumb Code   970  stm32f2xx_hal_gpio.o(.text.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x08000e65   Thumb Code    46  stm32f2xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08000e95   Thumb Code    12  stm32f2xx_hal.o(.text.HAL_GetTick)
    HAL_IncTick                              0x08000ea1   Thumb Code    26  stm32f2xx_hal.o(.text.HAL_IncTick)
    HAL_Init                                 0x08000ebd   Thumb Code    54  stm32f2xx_hal.o(.text.HAL_Init)
    HAL_InitTick                             0x08000ef5   Thumb Code   112  stm32f2xx_hal.o(.text.HAL_InitTick)
    HAL_MspInit                              0x08000f65   Thumb Code    74  stm32f2xx_hal_msp.o(.text.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08000fb1   Thumb Code    20  stm32f2xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08000fc5   Thumb Code    50  stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08000ff9   Thumb Code    16  stm32f2xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08001009   Thumb Code   600  stm32f2xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    HAL_RCC_GetSysClockFreq                  0x08001261   Thumb Code   230  stm32f2xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001349   Thumb Code  1578  stm32f2xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08001975   Thumb Code    16  stm32f2xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x08001985   Thumb Code     8  stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x0800198d   Thumb Code     8  stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_MasterConfigSynchronization    0x08001995   Thumb Code   290  stm32f2xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08001ab9   Thumb Code   156  stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08001b55   Thumb Code   204  stm32f2xx_hal_msp.o(.text.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08001c21   Thumb Code   246  stm32f2xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT)
    HAL_TIM_ConfigClockSource                0x08001d19   Thumb Code   388  stm32f2xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    HAL_TIM_IC_CaptureCallback               0x08001e9d   Thumb Code     8  stm32f2xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x08001ea5   Thumb Code   538  stm32f2xx_hal_tim.o(.text.HAL_TIM_IRQHandler)
    HAL_TIM_OC_DelayElapsedCallback          0x080020c1   Thumb Code     8  stm32f2xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_PulseFinishedCallback        0x080020c9   Thumb Code     8  stm32f2xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PeriodElapsedCallback            0x080020d1   Thumb Code   500  main.o(.text.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x080022c5   Thumb Code     8  stm32f2xx_hal_tim.o(.text.HAL_TIM_TriggerCallback)
    HardFault_Handler                        0x080022cd   Thumb Code     4  stm32f2xx_it.o(.text.HardFault_Handler)
    IOUART_Init                              0x080022d1   Thumb Code   322  main.o(.text.IOUART_Init)
    IOUART_PutChar                           0x08002415   Thumb Code   128  main.o(.text.IOUART_PutChar)
    IOUART_SendByte                          0x08002495   Thumb Code   140  main.o(.text.IOUART_SendByte)
    IOUART_SendString                        0x08002521   Thumb Code    60  main.o(.text.IOUART_SendString)
    MemManage_Handler                        0x080026ed   Thumb Code     4  stm32f2xx_it.o(.text.MemManage_Handler)
    NMI_Handler                              0x080026f1   Thumb Code     4  stm32f2xx_it.o(.text.NMI_Handler)
    PendSV_Handler                           0x08002761   Thumb Code     2  stm32f2xx_it.o(.text.PendSV_Handler)
    SVC_Handler                              0x08002765   Thumb Code     2  stm32f2xx_it.o(.text.SVC_Handler)
    SysTick_Handler                          0x080027bd   Thumb Code     8  stm32f2xx_it.o(.text.SysTick_Handler)
    SystemClock_Config                       0x080027c5   Thumb Code   118  main.o(.text.SystemClock_Config)
    SystemInit                               0x0800283d   Thumb Code     2  system_stm32f2xx.o(.text.SystemInit)
    TIM1_UP_TIM10_IRQHandler                 0x08002841   Thumb Code    16  stm32f2xx_it.o(.text.TIM1_UP_TIM10_IRQHandler)
    TIM3_IRQHandler                          0x08002851   Thumb Code    16  stm32f2xx_it.o(.text.TIM3_IRQHandler)
    TIM_Base_SetConfig                       0x08002861   Thumb Code   432  stm32f2xx_hal_tim.o(.text.TIM_Base_SetConfig)
    TIM_ETR_SetConfig                        0x08002a11   Thumb Code    52  stm32f2xx_hal_tim.o(.text.TIM_ETR_SetConfig)
    TestBoard_BuildPacket                    0x08002b15   Thumb Code   170  test_board_control.o(.text.TestBoard_BuildPacket)
    TestBoard_EmergencyStop                  0x08002bc1   Thumb Code    58  test_board_control.o(.text.TestBoard_EmergencyStop)
    TestBoard_GetInfo                        0x08002bfd   Thumb Code    52  test_board_control.o(.text.TestBoard_GetInfo)
    TestBoard_GetStatus                      0x08002c31   Thumb Code    60  test_board_control.o(.text.TestBoard_GetStatus)
    TestBoard_GetStatusName                  0x08002c6d   Thumb Code   108  test_board_control.o(.text.TestBoard_GetStatusName)
    TestBoard_GetTypeName                    0x08002cd9   Thumb Code   146  test_board_control.o(.text.TestBoard_GetTypeName)
    TestBoard_Init                           0x08002d6d   Thumb Code   200  test_board_control.o(.text.TestBoard_Init)
    TestBoard_PowerOff                       0x08002e35   Thumb Code   114  test_board_control.o(.text.TestBoard_PowerOff)
    TestBoard_PowerOffAll                    0x08002ea9   Thumb Code    80  test_board_control.o(.text.TestBoard_PowerOffAll)
    TestBoard_PowerOn                        0x08002ef9   Thumb Code   114  test_board_control.o(.text.TestBoard_PowerOn)
    TestBoard_PowerOnAll                     0x08002f6d   Thumb Code    80  test_board_control.o(.text.TestBoard_PowerOnAll)
    TestBoard_ProcessHeartbeat               0x08002fbd   Thumb Code   150  test_board_control.o(.text.TestBoard_ProcessHeartbeat)
    TestBoard_Reset                          0x08003055   Thumb Code   114  test_board_control.o(.text.TestBoard_Reset)
    TestBoard_RunAllTests                    0x080030c9   Thumb Code   120  test_board_test.o(.text.TestBoard_RunAllTests)
    TestBoard_SendPacket                     0x08003141   Thumb Code   128  test_board_control.o(.text.TestBoard_SendPacket)
    TestBoard_SetCurrent                     0x080031c1   Thumb Code   154  test_board_control.o(.text.TestBoard_SetCurrent)
    TestBoard_SetVoltage                     0x0800325d   Thumb Code   154  test_board_control.o(.text.TestBoard_SetVoltage)
    TestBoard_StartTest                      0x080032f9   Thumb Code   140  test_board_control.o(.text.TestBoard_StartTest)
    TestBoard_StopTest                       0x08003385   Thumb Code   114  test_board_control.o(.text.TestBoard_StopTest)
    TestBoard_UpdateStatus                   0x080033f9   Thumb Code   302  test_board_control.o(.text.TestBoard_UpdateStatus)
    TestBoard_ValidatePacket                 0x08003529   Thumb Code   158  test_board_control.o(.text.TestBoard_ValidatePacket)
    UsageFault_Handler                       0x080035c9   Thumb Code     4  stm32f2xx_it.o(.text.UsageFault_Handler)
    main                                     0x080036e5   Thumb Code   302  main.o(.text.main)
    sum_array                                0x08003819   Thumb Code    68  test_board_control.o(.text.sum_array)
    __0printf                                0x08004689   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x08004689   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x08004689   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x08004689   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x08004689   Thumb Code     0  printfa.o(i.__0printf)
    __0snprintf                              0x080046a9   Thumb Code    48  printfa.o(i.__0snprintf)
    __1snprintf                              0x080046a9   Thumb Code     0  printfa.o(i.__0snprintf)
    __2snprintf                              0x080046a9   Thumb Code     0  printfa.o(i.__0snprintf)
    __c89snprintf                            0x080046a9   Thumb Code     0  printfa.o(i.__0snprintf)
    snprintf                                 0x080046a9   Thumb Code     0  printfa.o(i.__0snprintf)
    __scatterload_copy                       0x080046dd   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080046eb   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080046ed   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    fputc                                    0x08004fc5   Thumb Code    18  fputc.o(i.fputc)
    AHBPrescTable                            0x08004fd6   Data          16  system_stm32f2xx.o(.rodata.AHBPrescTable)
    Region$$Table$$Base                      0x08005470   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08005490   Number         0  anon$$obj.o(Region$$Table)
    __stdout                                 0x20000000   Data           4  stdout.o(.data)
    SystemCoreClock                          0x20000004   Data           4  system_stm32f2xx.o(.data.SystemCoreClock)
    iouart_gpio                              0x20000008   Data         144  main.o(.data.iouart_gpio)
    uwTickFreq                               0x20000098   Data           1  stm32f2xx_hal.o(.data.uwTickFreq)
    uwTickPrio                               0x2000009c   Data           4  stm32f2xx_hal.o(.data.uwTickPrio)
    htim1                                    0x200001f4   Data          72  main.o(.bss.htim1)
    htim2                                    0x2000023c   Data          72  main.o(.bss.htim2)
    htim3                                    0x20000284   Data          72  main.o(.bss.htim3)
    iouart_buffer                            0x200002cc   Data        1260  main.o(.bss.iouart_buffer)
    uwTick                                   0x200007d0   Data           4  stm32f2xx_hal.o(.bss.uwTick)
    __initial_sp                             0x20000bd8   Data           0  startup_stm32f205xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000185

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00005530, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00005490, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000184   Data   RO            3    RESET               startup_stm32f205xx.o
    0x08000184   0x08000184   0x00000000   Code   RO          973  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000184   0x08000184   0x00000004   Code   RO         1016    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000188   0x08000188   0x00000004   Code   RO         1019    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x0800018c   0x0800018c   0x00000000   Code   RO         1021    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x0800018c   0x0800018c   0x00000000   Code   RO         1023    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x0800018c   0x0800018c   0x00000008   Code   RO         1024    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000194   0x08000194   0x00000000   Code   RO         1026    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000194   0x08000194   0x00000000   Code   RO         1028    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000194   0x08000194   0x00000004   Code   RO         1017    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000198   0x08000198   0x00000024   Code   RO            4    .text               startup_stm32f205xx.o
    0x080001bc   0x080001bc   0x00000062   Code   RO          976    .text               mc_w.l(uldiv.o)
    0x0800021e   0x0800021e   0x00000024   Code   RO          978    .text               mc_w.l(memcpya.o)
    0x08000242   0x08000242   0x00000024   Code   RO          980    .text               mc_w.l(memseta.o)
    0x08000266   0x08000266   0x0000001a   Code   RO          982    .text               mc_w.l(memcmp.o)
    0x08000280   0x08000280   0x00000012   Code   RO          984    .text               mc_w.l(strcpy.o)
    0x08000292   0x08000292   0x0000002c   Code   RO         1044    .text               mc_w.l(uidiv.o)
    0x080002be   0x080002be   0x0000001e   Code   RO         1046    .text               mc_w.l(llshl.o)
    0x080002dc   0x080002dc   0x00000020   Code   RO         1048    .text               mc_w.l(llushr.o)
    0x080002fc   0x080002fc   0x00000000   Code   RO         1050    .text               mc_w.l(iusefp.o)
    0x080002fc   0x080002fc   0x0000014e   Code   RO         1051    .text               mf_w.l(dadd.o)
    0x0800044a   0x0800044a   0x000000e4   Code   RO         1053    .text               mf_w.l(dmul.o)
    0x0800052e   0x0800052e   0x000000de   Code   RO         1055    .text               mf_w.l(ddiv.o)
    0x0800060c   0x0800060c   0x00000030   Code   RO         1057    .text               mf_w.l(dfixul.o)
    0x0800063c   0x0800063c   0x00000030   Code   RO         1059    .text               mf_w.l(cdrcmple.o)
    0x0800066c   0x0800066c   0x00000030   Code   RO         1061    .text               mc_w.l(init.o)
    0x0800069c   0x0800069c   0x00000000   Code   RO         1063    .text               mc_w.l(semi.o)
    0x0800069c   0x0800069c   0x00000024   Code   RO         1064    .text               mc_w.l(llsshr.o)
    0x080006c0   0x080006c0   0x00000000   Code   RO         1066    .text               mc_w.l(iusesemip.o)
    0x080006c0   0x080006c0   0x000000ba   Code   RO         1067    .text               mf_w.l(depilogue.o)
    0x0800077a   0x0800077a   0x00000002   PAD
    0x0800077c   0x0800077c   0x00000004   Code   RO           62    .text.BusFault_Handler  stm32f2xx_it.o
    0x08000780   0x08000780   0x00000002   Code   RO           68    .text.DebugMon_Handler  stm32f2xx_it.o
    0x08000782   0x08000782   0x00000002   PAD
    0x08000784   0x08000784   0x0000005e   Code   RO          140    .text.Display_Init  test_board_control.o
    0x080007e2   0x080007e2   0x00000002   PAD
    0x080007e4   0x080007e4   0x00000038   Code   RO          142    .text.Display_SendCommand  test_board_control.o
    0x0800081c   0x0800081c   0x00000048   Code   RO          148    .text.Display_SetComponent  test_board_control.o
    0x08000864   0x08000864   0x00000042   Code   RO          152    .text.Display_ShowBoardStatus  test_board_control.o
    0x080008a6   0x080008a6   0x00000002   PAD
    0x080008a8   0x080008a8   0x0000002a   Code   RO          144    .text.Display_ShowMessage  test_board_control.o
    0x080008d2   0x080008d2   0x00000002   PAD
    0x080008d4   0x080008d4   0x00000112   Code   RO          150    .text.Display_UpdateStatus  test_board_control.o
    0x080009e6   0x080009e6   0x00000002   PAD
    0x080009e8   0x080009e8   0x0000000c   Code   RO           78    .text.EXTI9_5_IRQHandler  stm32f2xx_it.o
    0x080009f4   0x080009f4   0x0000000e   Code   RO           25    .text.Error_Handler  main.o
    0x08000a02   0x08000a02   0x00000002   PAD
    0x08000a04   0x08000a04   0x00000042   Code   RO          553    .text.HAL_Delay     stm32f2xx_hal.o
    0x08000a46   0x08000a46   0x00000002   PAD
    0x08000a48   0x08000a48   0x00000018   Code   RO           23    .text.HAL_GPIO_EXTI_Callback  main.o
    0x08000a60   0x08000a60   0x00000038   Code   RO          871    .text.HAL_GPIO_EXTI_IRQHandler  stm32f2xx_hal_gpio.o
    0x08000a98   0x08000a98   0x000003ca   Code   RO          859    .text.HAL_GPIO_Init  stm32f2xx_hal_gpio.o
    0x08000e62   0x08000e62   0x00000002   PAD
    0x08000e64   0x08000e64   0x0000002e   Code   RO          865    .text.HAL_GPIO_WritePin  stm32f2xx_hal_gpio.o
    0x08000e92   0x08000e92   0x00000002   PAD
    0x08000e94   0x08000e94   0x0000000c   Code   RO          545    .text.HAL_GetTick   stm32f2xx_hal.o
    0x08000ea0   0x08000ea0   0x0000001a   Code   RO          543    .text.HAL_IncTick   stm32f2xx_hal.o
    0x08000eba   0x08000eba   0x00000002   PAD
    0x08000ebc   0x08000ebc   0x00000036   Code   RO          533    .text.HAL_Init      stm32f2xx_hal.o
    0x08000ef2   0x08000ef2   0x00000002   PAD
    0x08000ef4   0x08000ef4   0x00000070   Code   RO          535    .text.HAL_InitTick  stm32f2xx_hal.o
    0x08000f64   0x08000f64   0x0000004a   Code   RO           87    .text.HAL_MspInit   stm32f2xx_hal_msp.o
    0x08000fae   0x08000fae   0x00000002   PAD
    0x08000fb0   0x08000fb0   0x00000014   Code   RO          659    .text.HAL_NVIC_EnableIRQ  stm32f2xx_hal_cortex.o
    0x08000fc4   0x08000fc4   0x00000032   Code   RO          651    .text.HAL_NVIC_SetPriority  stm32f2xx_hal_cortex.o
    0x08000ff6   0x08000ff6   0x00000002   PAD
    0x08000ff8   0x08000ff8   0x00000010   Code   RO          647    .text.HAL_NVIC_SetPriorityGrouping  stm32f2xx_hal_cortex.o
    0x08001008   0x08001008   0x00000258   Code   RO          601    .text.HAL_RCC_ClockConfig  stm32f2xx_hal_rcc.o
    0x08001260   0x08001260   0x000000e6   Code   RO          603    .text.HAL_RCC_GetSysClockFreq  stm32f2xx_hal_rcc.o
    0x08001346   0x08001346   0x00000002   PAD
    0x08001348   0x08001348   0x0000062a   Code   RO          599    .text.HAL_RCC_OscConfig  stm32f2xx_hal_rcc.o
    0x08001972   0x08001972   0x00000002   PAD
    0x08001974   0x08001974   0x00000010   Code   RO          671    .text.HAL_SYSTICK_Config  stm32f2xx_hal_cortex.o
    0x08001984   0x08001984   0x00000008   Code   RO          520    .text.HAL_TIMEx_BreakCallback  stm32f2xx_hal_tim_ex.o
    0x0800198c   0x0800198c   0x00000008   Code   RO          516    .text.HAL_TIMEx_CommutCallback  stm32f2xx_hal_tim_ex.o
    0x08001994   0x08001994   0x00000122   Code   RO          510    .text.HAL_TIMEx_MasterConfigSynchronization  stm32f2xx_hal_tim_ex.o
    0x08001ab6   0x08001ab6   0x00000002   PAD
    0x08001ab8   0x08001ab8   0x0000009c   Code   RO          197    .text.HAL_TIM_Base_Init  stm32f2xx_hal_tim.o
    0x08001b54   0x08001b54   0x000000cc   Code   RO           89    .text.HAL_TIM_Base_MspInit  stm32f2xx_hal_msp.o
    0x08001c20   0x08001c20   0x000000f6   Code   RO          211    .text.HAL_TIM_Base_Start_IT  stm32f2xx_hal_tim.o
    0x08001d16   0x08001d16   0x00000002   PAD
    0x08001d18   0x08001d18   0x00000184   Code   RO          389    .text.HAL_TIM_ConfigClockSource  stm32f2xx_hal_tim.o
    0x08001e9c   0x08001e9c   0x00000008   Code   RO          333    .text.HAL_TIM_IC_CaptureCallback  stm32f2xx_hal_tim.o
    0x08001ea4   0x08001ea4   0x0000021a   Code   RO          331    .text.HAL_TIM_IRQHandler  stm32f2xx_hal_tim.o
    0x080020be   0x080020be   0x00000002   PAD
    0x080020c0   0x080020c0   0x00000008   Code   RO          335    .text.HAL_TIM_OC_DelayElapsedCallback  stm32f2xx_hal_tim.o
    0x080020c8   0x080020c8   0x00000008   Code   RO          337    .text.HAL_TIM_PWM_PulseFinishedCallback  stm32f2xx_hal_tim.o
    0x080020d0   0x080020d0   0x000001f4   Code   RO           17    .text.HAL_TIM_PeriodElapsedCallback  main.o
    0x080022c4   0x080022c4   0x00000008   Code   RO          341    .text.HAL_TIM_TriggerCallback  stm32f2xx_hal_tim.o
    0x080022cc   0x080022cc   0x00000004   Code   RO           58    .text.HardFault_Handler  stm32f2xx_it.o
    0x080022d0   0x080022d0   0x00000142   Code   RO           21    .text.IOUART_Init   main.o
    0x08002412   0x08002412   0x00000002   PAD
    0x08002414   0x08002414   0x00000080   Code   RO           11    .text.IOUART_PutChar  main.o
    0x08002494   0x08002494   0x0000008c   Code   RO           13    .text.IOUART_SendByte  main.o
    0x08002520   0x08002520   0x0000003c   Code   RO           15    .text.IOUART_SendString  main.o
    0x0800255c   0x0800255c   0x00000086   Code   RO           31    .text.MX_TIM1_Init  main.o
    0x080025e2   0x080025e2   0x00000002   PAD
    0x080025e4   0x080025e4   0x00000082   Code   RO           33    .text.MX_TIM2_Init  main.o
    0x08002666   0x08002666   0x00000002   PAD
    0x08002668   0x08002668   0x00000084   Code   RO           35    .text.MX_TIM3_Init  main.o
    0x080026ec   0x080026ec   0x00000004   Code   RO           60    .text.MemManage_Handler  stm32f2xx_it.o
    0x080026f0   0x080026f0   0x00000004   Code   RO           56    .text.NMI_Handler   stm32f2xx_it.o
    0x080026f4   0x080026f4   0x0000006c   Code   RO          657    .text.NVIC_EncodePriority  stm32f2xx_hal_cortex.o
    0x08002760   0x08002760   0x00000002   Code   RO           70    .text.PendSV_Handler  stm32f2xx_it.o
    0x08002762   0x08002762   0x00000002   PAD
    0x08002764   0x08002764   0x00000002   Code   RO           66    .text.SVC_Handler   stm32f2xx_it.o
    0x08002766   0x08002766   0x00000002   PAD
    0x08002768   0x08002768   0x00000052   Code   RO          673    .text.SysTick_Config  stm32f2xx_hal_cortex.o
    0x080027ba   0x080027ba   0x00000002   PAD
    0x080027bc   0x080027bc   0x00000008   Code   RO           72    .text.SysTick_Handler  stm32f2xx_it.o
    0x080027c4   0x080027c4   0x00000076   Code   RO           29    .text.SystemClock_Config  main.o
    0x0800283a   0x0800283a   0x00000002   PAD
    0x0800283c   0x0800283c   0x00000002   Code   RO          960    .text.SystemInit    system_stm32f2xx.o
    0x0800283e   0x0800283e   0x00000002   PAD
    0x08002840   0x08002840   0x00000010   Code   RO           74    .text.TIM1_UP_TIM10_IRQHandler  stm32f2xx_it.o
    0x08002850   0x08002850   0x00000010   Code   RO           76    .text.TIM3_IRQHandler  stm32f2xx_it.o
    0x08002860   0x08002860   0x000001b0   Code   RO          201    .text.TIM_Base_SetConfig  stm32f2xx_hal_tim.o
    0x08002a10   0x08002a10   0x00000034   Code   RO          387    .text.TIM_ETR_SetConfig  stm32f2xx_hal_tim.o
    0x08002a44   0x08002a44   0x0000002a   Code   RO          393    .text.TIM_ITRx_SetConfig  stm32f2xx_hal_tim.o
    0x08002a6e   0x08002a6e   0x00000002   PAD
    0x08002a70   0x08002a70   0x00000050   Code   RO          391    .text.TIM_TI1_ConfigInputStage  stm32f2xx_hal_tim.o
    0x08002ac0   0x08002ac0   0x00000052   Code   RO          395    .text.TIM_TI2_ConfigInputStage  stm32f2xx_hal_tim.o
    0x08002b12   0x08002b12   0x00000002   PAD
    0x08002b14   0x08002b14   0x000000aa   Code   RO          108    .text.TestBoard_BuildPacket  test_board_control.o
    0x08002bbe   0x08002bbe   0x00000002   PAD
    0x08002bc0   0x08002bc0   0x0000003a   Code   RO          136    .text.TestBoard_EmergencyStop  test_board_control.o
    0x08002bfa   0x08002bfa   0x00000002   PAD
    0x08002bfc   0x08002bfc   0x00000034   Code   RO          138    .text.TestBoard_GetInfo  test_board_control.o
    0x08002c30   0x08002c30   0x0000003c   Code   RO          122    .text.TestBoard_GetStatus  test_board_control.o
    0x08002c6c   0x08002c6c   0x0000006c   Code   RO          104    .text.TestBoard_GetStatusName  test_board_control.o
    0x08002cd8   0x08002cd8   0x00000092   Code   RO          102    .text.TestBoard_GetTypeName  test_board_control.o
    0x08002d6a   0x08002d6a   0x00000002   PAD
    0x08002d6c   0x08002d6c   0x000000c8   Code   RO          106    .text.TestBoard_Init  test_board_control.o
    0x08002e34   0x08002e34   0x00000072   Code   RO          118    .text.TestBoard_PowerOff  test_board_control.o
    0x08002ea6   0x08002ea6   0x00000002   PAD
    0x08002ea8   0x08002ea8   0x00000050   Code   RO          134    .text.TestBoard_PowerOffAll  test_board_control.o
    0x08002ef8   0x08002ef8   0x00000072   Code   RO          114    .text.TestBoard_PowerOn  test_board_control.o
    0x08002f6a   0x08002f6a   0x00000002   PAD
    0x08002f6c   0x08002f6c   0x00000050   Code   RO          132    .text.TestBoard_PowerOnAll  test_board_control.o
    0x08002fbc   0x08002fbc   0x00000096   Code   RO          156    .text.TestBoard_ProcessHeartbeat  test_board_control.o
    0x08003052   0x08003052   0x00000002   PAD
    0x08003054   0x08003054   0x00000072   Code   RO          120    .text.TestBoard_Reset  test_board_control.o
    0x080030c6   0x080030c6   0x00000002   PAD
    0x080030c8   0x080030c8   0x00000078   Code   RO          169    .text.TestBoard_RunAllTests  test_board_test.o
    0x08003140   0x08003140   0x00000080   Code   RO          112    .text.TestBoard_SendPacket  test_board_control.o
    0x080031c0   0x080031c0   0x0000009a   Code   RO          126    .text.TestBoard_SetCurrent  test_board_control.o
    0x0800325a   0x0800325a   0x00000002   PAD
    0x0800325c   0x0800325c   0x0000009a   Code   RO          124    .text.TestBoard_SetVoltage  test_board_control.o
    0x080032f6   0x080032f6   0x00000002   PAD
    0x080032f8   0x080032f8   0x0000008c   Code   RO          128    .text.TestBoard_StartTest  test_board_control.o
    0x08003384   0x08003384   0x00000072   Code   RO          130    .text.TestBoard_StopTest  test_board_control.o
    0x080033f6   0x080033f6   0x00000002   PAD
    0x080033f8   0x080033f8   0x0000012e   Code   RO          154    .text.TestBoard_UpdateStatus  test_board_control.o
    0x08003526   0x08003526   0x00000002   PAD
    0x08003528   0x08003528   0x0000009e   Code   RO          110    .text.TestBoard_ValidatePacket  test_board_control.o
    0x080035c6   0x080035c6   0x00000002   PAD
    0x080035c8   0x080035c8   0x00000004   Code   RO           64    .text.UsageFault_Handler  stm32f2xx_it.o
    0x080035cc   0x080035cc   0x00000030   Code   RO          661    .text.__NVIC_EnableIRQ  stm32f2xx_hal_cortex.o
    0x080035fc   0x080035fc   0x00000010   Code   RO          653    .text.__NVIC_GetPriorityGrouping  stm32f2xx_hal_cortex.o
    0x0800360c   0x0800360c   0x00000042   Code   RO          655    .text.__NVIC_SetPriority  stm32f2xx_hal_cortex.o
    0x0800364e   0x0800364e   0x00000002   PAD
    0x08003650   0x08003650   0x0000003c   Code   RO          649    .text.__NVIC_SetPriorityGrouping  stm32f2xx_hal_cortex.o
    0x0800368c   0x0800368c   0x00000058   Code   RO           19    .text.iouart_TXD    main.o
    0x080036e4   0x080036e4   0x0000012e   Code   RO           27    .text.main          main.o
    0x08003812   0x08003812   0x00000002   PAD
    0x08003814   0x08003814   0x00000002   Code   RO          181    .text.print_test_summary  test_board_test.o
    0x08003816   0x08003816   0x00000002   PAD
    0x08003818   0x08003818   0x00000044   Code   RO          100    .text.sum_array     test_board_control.o
    0x0800385c   0x0800385c   0x000005f0   Code   RO          175    .text.test_board_control_commands  test_board_test.o
    0x08003e4c   0x08003e4c   0x00000116   Code   RO          177    .text.test_display_functions  test_board_test.o
    0x08003f62   0x08003f62   0x00000002   PAD
    0x08003f64   0x08003f64   0x00000214   Code   RO          179    .text.test_error_handling  test_board_test.o
    0x08004178   0x08004178   0x000003fe   Code   RO          173    .text.test_packet_validation  test_board_test.o
    0x08004576   0x08004576   0x00000002   PAD
    0x08004578   0x08004578   0x00000080   Code   RO          171    .text.test_uart_communication  test_board_test.o
    0x080045f8   0x080045f8   0x0000008e   Code   RO          116    .text.update_board_info  test_board_control.o
    0x08004686   0x08004686   0x00000002   PAD
    0x08004688   0x08004688   0x00000020   Code   RO          989    i.__0printf         mc_w.l(printfa.o)
    0x080046a8   0x080046a8   0x00000034   Code   RO          990    i.__0snprintf       mc_w.l(printfa.o)
    0x080046dc   0x080046dc   0x0000000e   Code   RO         1071    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080046ea   0x080046ea   0x00000002   Code   RO         1072    i.__scatterload_null  mc_w.l(handlers.o)
    0x080046ec   0x080046ec   0x0000000e   Code   RO         1073    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080046fa   0x080046fa   0x00000002   PAD
    0x080046fc   0x080046fc   0x00000184   Code   RO          996    i._fp_digits        mc_w.l(printfa.o)
    0x08004880   0x08004880   0x000006dc   Code   RO          997    i._printf_core      mc_w.l(printfa.o)
    0x08004f5c   0x08004f5c   0x00000024   Code   RO          998    i._printf_post_padding  mc_w.l(printfa.o)
    0x08004f80   0x08004f80   0x0000002e   Code   RO          999    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08004fae   0x08004fae   0x00000016   Code   RO         1000    i._snputc           mc_w.l(printfa.o)
    0x08004fc4   0x08004fc4   0x00000012   Code   RO         1030    i.fputc             mc_w.l(fputc.o)
    0x08004fd6   0x08004fd6   0x00000010   Data   RO          965    .rodata.AHBPrescTable  system_stm32f2xx.o
    0x08004fe6   0x08004fe6   0x00000066   Data   RO           44    .rodata.str1.1      main.o
    0x0800504c   0x0800504c   0x000000b5   Data   RO          158    .rodata.str1.1      test_board_control.o
    0x08005101   0x08005101   0x0000036d   Data   RO          188    .rodata.str1.1      test_board_test.o
    0x0800546e   0x0800546e   0x00000002   PAD
    0x08005470   0x08005470   0x00000020   Data   RO         1070    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08005490, Size: 0x00000bd8, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08005490   0x00000004   Data   RW         1034    .data               mc_w.l(stdout.o)
    0x20000004   0x08005494   0x00000004   Data   RW          964    .data.SystemCoreClock  system_stm32f2xx.o
    0x20000008   0x08005498   0x00000090   Data   RW           39    .data.iouart_gpio   main.o
    0x20000098   0x08005528   0x00000001   Data   RW          588    .data.uwTickFreq    stm32f2xx_hal.o
    0x20000099   0x08005529   0x00000003   PAD
    0x2000009c   0x0800552c   0x00000004   Data   RW          587    .data.uwTickPrio    stm32f2xx_hal.o
    0x200000a0        -       0x00000004   Zero   RW           41    .bss.HAL_TIM_PeriodElapsedCallback.global_time_us  main.o
    0x200000a4        -       0x00000004   Zero   RW          161    .bss.TestBoard_ProcessHeartbeat.last_heartbeat_time  test_board_control.o
    0x200000a8        -       0x000000c0   Zero   RW          159    .bss.board_info     test_board_control.o
    0x20000168        -       0x0000008c   Zero   RW          160    .bss.display_info   test_board_control.o
    0x200001f4        -       0x00000048   Zero   RW           47    .bss.htim1          main.o
    0x2000023c        -       0x00000048   Zero   RW           42    .bss.htim2          main.o
    0x20000284        -       0x00000048   Zero   RW           43    .bss.htim3          main.o
    0x200002cc        -       0x000004ec   Zero   RW           40    .bss.iouart_buffer  main.o
    0x200007b8        -       0x00000004   Zero   RW           46    .bss.main.last_test_time  main.o
    0x200007bc        -       0x00000001   Zero   RW           45    .bss.main.test_executed  main.o
    0x200007bd   0x08005530   0x00000003   PAD
    0x200007c0        -       0x00000010   Zero   RW          187    .bss.test_result    test_board_test.o
    0x200007d0        -       0x00000004   Zero   RW          589    .bss.uwTick         stm32f2xx_hal.o
    0x200007d4   0x08005530   0x00000004   PAD
    0x200007d8        -       0x00000400   Zero   RW            1    STACK               startup_stm32f205xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x08005530, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      2092          0        102        144       1485      10050   main.o
        36          8        388          0       1024        772   startup_stm32f205xx.o
       270          0          0          5          4       7035   stm32f2xx_hal.o
       482          0          0          0          0       9552   stm32f2xx_hal_cortex.o
      1072          0          0          0          0       4964   stm32f2xx_hal_gpio.o
       278          0          0          0          0       6676   stm32f2xx_hal_msp.o
      2408          0          0          0          0       7758   stm32f2xx_hal_rcc.o
      2048          0          0          0          0      35989   stm32f2xx_hal_tim.o
       306          0          0          0          0      15307   stm32f2xx_hal_tim_ex.o
        78          0          0          0          0       1411   stm32f2xx_it.o
         2          0         16          4          0       1978   system_stm32f2xx.o
      3410         14        181          0        336       8959   test_board_control.o
      3602          0        877          0         16       5413   test_board_test.o

    ----------------------------------------------------------------------
     16176         <USER>       <GROUP>        156       2872     115864   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        92          0          2          3          7          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        18          0          0          0          0         80   fputc.o
        30          0          0          0          0          0   handlers.o
        48         10          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
         0          0          0          0          0          0   iusesemip.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        26          0          0          0          0         80   memcmp.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2332         94          0          0          0        608   printfa.o
         0          0          0          0          0          0   semi.o
         0          0          0          4          0          0   stdout.o
        18          0          0          0          0         68   strcpy.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o

    ----------------------------------------------------------------------
      3874        <USER>          <GROUP>          4          0       2112   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2804        112          0          4          0       1456   mc_w.l
      1066          0          0          0          0        656   mf_w.l

    ----------------------------------------------------------------------
      3874        <USER>          <GROUP>          4          0       2112   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     20050        134       1598        160       2872     116764   Grand Totals
     20050        134       1598        160       2872     116764   ELF Image Totals
     20050        134       1598        160          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                21648 (  21.14kB)
    Total RW  Size (RW Data + ZI Data)              3032 (   2.96kB)
    Total ROM Size (Code + RO Data + RW Data)      21808 (  21.30kB)

==============================================================================

