/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "test_board_control.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

// IO模拟串口相关定义
#define IO_USART_SENDDELAY_TIME   8681  // 数据发送间隔时间(115200bps)
#define COM_STOP_BIT              0x00  // 停止位状态

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
TIM_HandleTypeDef htim1;
TIM_HandleTypeDef htim2;
TIM_HandleTypeDef htim3;

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
//static void MX_GPIO_Init(void);
static void MX_TIM1_Init(void);
static void MX_TIM2_Init(void);
static void MX_TIM3_Init(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
// IO模拟串口: 使用TIM2作为时间基准，PC7为TX，PC8为RX
uint8_t recvData = 0;  //接收数据
uint8_t recvStat = COM_STOP_BIT;  //接受状态

// TIM2 预分频=99，APB1定时器时钟=50MHz => 计数频率=50MHz/100=0.5MHz => 1计数=2us
static inline uint16_t iouart_us_to_ticks(uint32_t us)
{
  return (uint16_t)(us / 2U);  // 2us分辨率
}

static inline void iouart_delay_us(uint32_t us)
{
  uint16_t ticks = iouart_us_to_ticks(us);
  __HAL_TIM_SET_COUNTER(&htim2, 0);
  while (__HAL_TIM_GET_COUNTER(&htim2) < ticks) {}
}

static inline void iouart_delay_1bit(void)
{
  iouart_delay_us(IO_USART_SENDDELAY_TIME);
}

/* 定义9个虚拟串口（包含大彩串口屏） */
#define IOUART_NUM 9

/* 每个串口的GPIO定义 */
typedef struct {
    GPIO_TypeDef* tx_port;
    uint16_t tx_pin;
    GPIO_TypeDef* rx_port;
    uint16_t rx_pin;
} IOUART_GPIO_t;

/* 串口状态和缓冲区 */
//typedef struct {
//    uint8_t tx_buf[64];
//    uint8_t tx_head;
//    uint8_t tx_tail;
//    uint8_t rx_buf[64];
//    uint8_t rx_head;
//    uint8_t rx_tail;
//    uint8_t busy;
//} IOUART_Buffer_t;


// 1. 使用状态机替代阻塞延时
typedef enum {
    IOUART_IDLE,      // 空闲状态
    IOUART_START_BIT, // 发送起始位
    IOUART_DATA_BIT,  // 发送数据位
    IOUART_STOP_BIT   // 发送停止位
} IOUART_State_t;

// 扩展串口缓冲区结构体，添加状态机变量
typedef struct {
    uint8_t tx_buf[64];
    uint8_t tx_head;
    uint8_t tx_tail;
    uint8_t rx_buf[64];
    uint8_t rx_head;
    uint8_t rx_tail;
    IOUART_State_t state;   // 当前状态
    uint8_t current_byte;   // 当前正在发送的字节
    uint8_t bit_position;   // 当前位位置(0-7)
    uint32_t next_bit_time; // 下一位发送时间
} IOUART_Buffer_t;

/* 定义9个串口的GPIO映射 - 根据引脚功能表（已修正冲突） */
IOUART_GPIO_t iouart_gpio[IOUART_NUM] = {
    {GPIOB, GPIO_PIN_1, GPIOB, GPIO_PIN_2},   // UART0: EL1_UART - 测试板1-RS232接口 (PB1/PB2)
    {GPIOA, GPIO_PIN_5, GPIOA, GPIO_PIN_6},   // UART1: EL2_UART - 测试板2-RS232接口 (PA5/PA6) 
    {GPIOC, GPIO_PIN_12, GPIOD, GPIO_PIN_2},   // UART2: EL3_UART - 测试板3-RS232接口 (PC12/PD2)
    {GPIOC, GPIO_PIN_6, GPIOC, GPIO_PIN_7},   // UART3: EL4_UART - 测试板4-RS232接口 (PC6/PC7)
    {GPIOA, GPIO_PIN_7, GPIOC, GPIO_PIN_4},   // UART4: USART_TEC1 - TEC1~RS232接口 (PA7/PC4) 
    {GPIOC, GPIO_PIN_5, GPIOB, GPIO_PIN_0},   // UART5: USART_TEC2 - TEC2~RS232接口 (PC5/PB0)
    {GPIOB, GPIO_PIN_10, GPIOB, GPIO_PIN_11}, // UART6: USART_TEC3 - TEC3~RS232接口 (PB10/PB11)
    {GPIOA, GPIO_PIN_8, GPIOC, GPIO_PIN_9},   // UART7: USART_TEC4 - TEC4~RS232接口 (PA8/PC9)
    {GPIOC, GPIO_PIN_10, GPIOC, GPIO_PIN_11}  // UART8: 大彩串口屏 - DC48270M043_1111_0C (PC10/PC11)
};

/* 9个串口的缓冲区 */
IOUART_Buffer_t iouart_buffer[IOUART_NUM] = {0};

/* 设置指定串口的TX引脚 */
static inline void iouart_TXD(uint8_t uart_id, uint8_t value) {
    if (uart_id >= IOUART_NUM) return;
    
    if (value) {
        iouart_gpio[uart_id].tx_port->BSRR = iouart_gpio[uart_id].tx_pin;
    } else {
        iouart_gpio[uart_id].tx_port->BSRR = (iouart_gpio[uart_id].tx_pin << 16);
    }
}

/* 读取指定串口的RX引脚 */
static inline GPIO_PinState iouart_RXD(uint8_t uart_id) {
    if (uart_id >= IOUART_NUM) return GPIO_PIN_RESET;
    
    return HAL_GPIO_ReadPin(iouart_gpio[uart_id].rx_port, iouart_gpio[uart_id].rx_pin);
}



/* 向指定串口的发送缓冲区添加数据 */
void IOUART_PutChar(uint8_t uart_id, uint8_t ch) {
    if (uart_id >= IOUART_NUM) return;
    
    uint8_t next = (iouart_buffer[uart_id].tx_head + 1) % sizeof(iouart_buffer[uart_id].tx_buf);
    if (next != iouart_buffer[uart_id].tx_tail) {
        iouart_buffer[uart_id].tx_buf[iouart_buffer[uart_id].tx_head] = ch;
        iouart_buffer[uart_id].tx_head = next;
    }
}
/* 发送一个字节到指定串口 - 非阻塞版本 */
void IOUART_SendByte(uint8_t uart_id, uint8_t ch) {
    if (uart_id >= IOUART_NUM) return;
    
    // 检查串口是否忙碌
    if (iouart_buffer[uart_id].state != IOUART_IDLE) {
        // 如果忙碌，添加到发送缓冲区
        IOUART_PutChar(uart_id, ch);
        return;
    }
    
    // 如果空闲，直接启动发送
    iouart_buffer[uart_id].current_byte = ch;
    iouart_buffer[uart_id].state = IOUART_START_BIT;
    iouart_buffer[uart_id].next_bit_time = HAL_GetTick() * 1000; // 转换为微秒
}



/* 发送字符串到指定串口 */
void IOUART_SendString(uint8_t uart_id, const char *s) {
    if (!s || uart_id >= IOUART_NUM) return;
    
    while (*s) {
        IOUART_PutChar(uart_id, (uint8_t)*s++);
    }
}

/* 改进的多串口设计 */

// 定义更高频率的定时器
#define BIT_TIMER_FREQ 1000000 // 1MHz，提供1us分辨率
#define BIT_TIME_115200 (BIT_TIMER_FREQ/115200) // 115200bps的位时间

// 在高优先级定时器中断中处理位发送
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim) {
    static uint32_t global_time_us = 0;
    
    if (htim->Instance == TIM2) { // TIM2作为位时序定时器，2us分辨率
        global_time_us += 2; // 每次中断增加2us
        
        // 处理每个串口的状态机
        for (uint8_t i = 0; i < IOUART_NUM; i++) {
            IOUART_Buffer_t *uart = &iouart_buffer[i];
            
            // 检查是否到达下一位时间
            if (uart->state != IOUART_IDLE && global_time_us >= uart->next_bit_time) {
                switch (uart->state) {
                    case IOUART_START_BIT:
                        // 发送起始位
                        iouart_TXD(i, 0);
                        uart->bit_position = 0;
                        uart->next_bit_time = global_time_us + IO_USART_SENDDELAY_TIME;
                        uart->state = IOUART_DATA_BIT;
                        break;
                        
                    case IOUART_DATA_BIT:
                        // 发送数据位
                        iouart_TXD(i, (uart->current_byte >> uart->bit_position) & 0x01);
                        uart->bit_position++;
                        uart->next_bit_time = global_time_us + IO_USART_SENDDELAY_TIME;
                        
                        if (uart->bit_position >= 8) {
                            uart->state = IOUART_STOP_BIT;
                        }
                        break;
                        
                    case IOUART_STOP_BIT:
                        // 发送停止位
                        iouart_TXD(i, 1);
                        uart->next_bit_time = global_time_us + IO_USART_SENDDELAY_TIME;
                        uart->state = IOUART_IDLE;
                        break;
                        
                    default:
                        uart->state = IOUART_IDLE;
                        break;
                }
            }
        }
    }
    
    if (htim->Instance == TIM3) { // 低优先级定时器用于启动新的传输
        // 检查每个串口是否有新数据需要发送
        for (uint8_t i = 0; i < IOUART_NUM; i++) {
            IOUART_Buffer_t *uart = &iouart_buffer[i];
            
            // 如果串口空闲且有数据要发送
            if (uart->state == IOUART_IDLE && uart->tx_head != uart->tx_tail) {
                uart->current_byte = uart->tx_buf[uart->tx_tail];
                uart->tx_tail = (uart->tx_tail + 1) % sizeof(uart->tx_buf);
                uart->state = IOUART_START_BIT;
                uart->next_bit_time = global_time_us; // 立即开始发送
            }
        }
    }
}

/* 初始化所有虚拟串口的GPIO */
void IOUART_Init(void) {
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 确保所有用到的GPIO时钟已启用
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_GPIOB_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();
    __HAL_RCC_GPIOD_CLK_ENABLE();
    
    // 初始化所有串口的TX和RX引脚
    for (uint8_t i = 0; i < IOUART_NUM; i++) {
        // 配置TX引脚为推挽输出，高电平
        GPIO_InitStruct.Pin = iouart_gpio[i].tx_pin;
        GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
        GPIO_InitStruct.Pull = GPIO_PULLUP;
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
        HAL_GPIO_Init(iouart_gpio[i].tx_port, &GPIO_InitStruct);
        HAL_GPIO_WritePin(iouart_gpio[i].tx_port, iouart_gpio[i].tx_pin, GPIO_PIN_SET);
        
        // 配置RX引脚为输入，上拉
        GPIO_InitStruct.Pin = iouart_gpio[i].rx_pin;
        GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
        GPIO_InitStruct.Pull = GPIO_PULLUP;
        HAL_GPIO_Init(iouart_gpio[i].rx_port, &GPIO_InitStruct);
    }
    
    // 启动TIM2用于位时序控制
    HAL_TIM_Base_Start_IT(&htim2);
    
    // 启动TIM3用于轮询发送
    HAL_TIM_Base_Start_IT(&htim3);
}

// 可选：在起始位到来时由中断触发，示例中断回调，仅示例，不做阻塞采样
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
  if (GPIO_Pin == GPIO_PIN_8)
  {
    // 如需在中断内做采样，请谨慎控制时延（阻塞会影响系统）。
    // 本示例保持空，实现由主循环使用 IOUART_ReceiveByte 进行阻塞接收即可。
  }
}

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */


int main(void) {
    /* HAL库初始化 */
    HAL_Init();
    
    /* 配置系统时钟 */
    SystemClock_Config();
    
    /* 初始化GPIO */
    //MX_GPIO_Init();
    
    /* 初始化定时器 */
    MX_TIM1_Init();
    MX_TIM2_Init();
    MX_TIM3_Init();
    
    /* 初始化所有虚拟串口 */
    IOUART_Init();
    
    /* 初始化测试板控制系统 */
    TestBoard_Init();
    
    /* 初始化大彩串口屏 */
    Display_Init();
    
    /* 显示初始化完成消息 */
    Display_ShowMessage("Control Board Ready!");
    
    /* Infinite loop */
    static bool test_executed = false;
    
    while (1) {
        // 更新系统状态
        TestBoard_UpdateStatus();
        
        // 处理心跳数据包
        TestBoard_ProcessHeartbeat();
        
        // 更新大彩串口屏显示
        Display_UpdateStatus();
        
        // 运行一次测试（启动后10秒）
        if (!test_executed && HAL_GetTick() > 10000) {
            Display_ShowMessage("Starting System Test...");
            HAL_Delay(1000);
            
            // 运行所有测试
            bool test_result = TestBoard_RunAllTests();
            
            if (test_result) {
                Display_ShowMessage("All Tests PASSED!");
            } else {
                Display_ShowMessage("Some Tests FAILED!");
            }
            
            test_executed = true;
        }
        
        // 测试发送到不同串口 - 根据引脚功能表对应的接口
        static uint32_t last_test_time = 0;
        uint32_t current_time = HAL_GetTick();
        
        if (current_time - last_test_time >= 5000) { // 每5秒测试一次
            // 测试测试板通信
            TestBoard_PowerOn(TEST_BOARD_EL1);
            HAL_Delay(100);
            TestBoard_SetVoltage(TEST_BOARD_EL1, 3300); // 3.3V
            HAL_Delay(100);
            TestBoard_StartTest(TEST_BOARD_EL1);
            
            // 测试TEC板通信
            TestBoard_PowerOn(TEC_BOARD_1);
            HAL_Delay(100);
            TestBoard_SetCurrent(TEC_BOARD_1, 1000); // 1A
            
            Display_ShowMessage("Test Cycle Complete");
            last_test_time = current_time;
        }
        
        HAL_Delay(10); // 简单延时避免过度占用CPU
    }
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLM = 8;
  RCC_OscInitStruct.PLL.PLLN = 200;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV4;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_3) != HAL_OK)
  {
    Error_Handler();
  }
}

/**
  * @brief TIM1 Initialization Function
  * @param None
  * @retval None
  */
static void MX_TIM1_Init(void)
{

  /* USER CODE BEGIN TIM1_Init 0 */

  /* USER CODE END TIM1_Init 0 */

  TIM_ClockConfigTypeDef sClockSourceConfig = {0};
  TIM_MasterConfigTypeDef sMasterConfig = {0};

  /* USER CODE BEGIN TIM1_Init 1 */

  /* USER CODE END TIM1_Init 1 */
  htim1.Instance = TIM1;
  htim1.Init.Prescaler = 99;
  htim1.Init.CounterMode = TIM_COUNTERMODE_UP;
  htim1.Init.Period = 100;
  htim1.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
  htim1.Init.RepetitionCounter = 0;
  htim1.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;
  if (HAL_TIM_Base_Init(&htim1) != HAL_OK)
  {
    Error_Handler();
  }
  sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
  if (HAL_TIM_ConfigClockSource(&htim1, &sClockSourceConfig) != HAL_OK)
  {
    Error_Handler();
  }
  sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
  sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
  if (HAL_TIMEx_MasterConfigSynchronization(&htim1, &sMasterConfig) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN TIM1_Init 2 */

  /* USER CODE END TIM1_Init 2 */

}

/**
  * @brief TIM2 Initialization Function
  * @param None
  * @retval None
  */
static void MX_TIM2_Init(void)
{

  /* USER CODE BEGIN TIM2_Init 0 */

  /* USER CODE END TIM2_Init 0 */

  TIM_ClockConfigTypeDef sClockSourceConfig = {0};
  TIM_MasterConfigTypeDef sMasterConfig = {0};

  /* USER CODE BEGIN TIM2_Init 1 */

  /* USER CODE END TIM2_Init 1 */
  htim2.Instance = TIM2;
  htim2.Init.Prescaler = 99;
  htim2.Init.CounterMode = TIM_COUNTERMODE_UP;
  htim2.Init.Period = 65535;
  htim2.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
  htim2.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;
  if (HAL_TIM_Base_Init(&htim2) != HAL_OK)
  {
    Error_Handler();
  }
  sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
  if (HAL_TIM_ConfigClockSource(&htim2, &sClockSourceConfig) != HAL_OK)
  {
    Error_Handler();
  }
  sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
  sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
  if (HAL_TIMEx_MasterConfigSynchronization(&htim2, &sMasterConfig) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN TIM2_Init 2 */

  /* USER CODE END TIM2_Init 2 */

}

/**
  * @brief TIM3 Initialization Function
  * @param None
  * @retval None
  */
static void MX_TIM3_Init(void)
{

  /* USER CODE BEGIN TIM3_Init 0 */

  /* USER CODE END TIM3_Init 0 */

  TIM_ClockConfigTypeDef sClockSourceConfig = {0};
  TIM_MasterConfigTypeDef sMasterConfig = {0};

  /* USER CODE BEGIN TIM3_Init 1 */

  /* USER CODE END TIM3_Init 1 */
  htim3.Instance = TIM3;
  htim3.Init.Prescaler = 99;
  htim3.Init.CounterMode = TIM_COUNTERMODE_UP;
  htim3.Init.Period = 100;
  htim3.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
  htim3.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;
  if (HAL_TIM_Base_Init(&htim3) != HAL_OK)
  {
    Error_Handler();
  }
  sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
  if (HAL_TIM_ConfigClockSource(&htim3, &sClockSourceConfig) != HAL_OK)
  {
    Error_Handler();
  }
  sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
  sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
  if (HAL_TIMEx_MasterConfigSynchronization(&htim3, &sMasterConfig) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN TIM3_Init 2 */

  /* USER CODE END TIM3_Init 2 */

}


/* USER CODE BEGIN 4 */

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */