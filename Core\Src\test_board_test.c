/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file           : test_board_test.c
 * @brief          : 测试板控制系统测试程序
 ******************************************************************************
 * @attention
 *
 * 测试程序用于验证:
 * 1. 各个串口的通信功能
 * 2. 测试板控制指令
 * 3. 串口屏显示功能
 * 4. 错误处理机制
 *
 ******************************************************************************
 */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "test_board_control.h"
#include "main.h"
#include <stdio.h>
#include <string.h>

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

typedef struct
{
    uint32_t test_count;
    uint32_t pass_count;
    uint32_t fail_count;
    bool test_running;
} TestResult_t;

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

#define TEST_TIMEOUT_MS 5000
#define TEST_DELAY_MS 100

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

#define TEST_ASSERT(condition)                                                   \
    do                                                                           \
    {                                                                            \
        test_result.test_count++;                                                \
        if (condition)                                                           \
        {                                                                        \
            test_result.pass_count++;                                            \
            printf("[PASS] Test %u: %s\n", test_result.test_count, #condition); \
        }                                                                        \
        else                                                                     \
        {                                                                        \
            test_result.fail_count++;                                            \
            printf("[FAIL] Test %u: %s\n", test_result.test_count, #condition); \
        }                                                                        \
    } while (0)

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN PV */

static TestResult_t test_result = {0};

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN PFP */

static bool test_uart_communication(void);
static bool test_board_control_commands(void);
static bool test_display_functions(void);
static bool test_packet_validation(void);
static bool test_error_handling(void);
static void print_test_summary(void);

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/**
 * @brief 运行所有测试
 * @retval true: 全部通过, false: 有测试失败
 */
bool TestBoard_RunAllTests(void)
{
    //printf("\n=== 测试板控制系统测试开始 ===\n");

    // 初始化测试结果
    memset(&test_result, 0, sizeof(TestResult_t));
    test_result.test_running = true;

    // 显示测试开始信息
    Display_ShowMessage("Running Tests...");

    // 运行各项测试
    test_uart_communication();
    test_packet_validation();
    test_board_control_commands();
    test_display_functions();
    test_error_handling();

    // 完成测试
    test_result.test_running = false;

    // 打印测试总结
    print_test_summary();

    // 显示测试结果
    char result_msg[64];
    snprintf(result_msg, sizeof(result_msg), "Tests: %u/%u PASS",
             test_result.pass_count, test_result.test_count);
    Display_ShowMessage(result_msg);

    return (test_result.fail_count == 0);
}
/** @brief 测试串口通信功能
 @retval true: 通过, false: 失败
 */
static bool test_uart_communication(void)
{
    //printf("--- 测试串口通信功能 ---");
    // 测试虚拟串口发送函数
    for (uint8_t i = 0; i < 9; i++)
    {
        char test_msg[32];
        snprintf(test_msg, sizeof(test_msg), "UART%d Test", i);
        // 发送测试消息（这里只能测试发送，无法验证接收）
        IOUART_SendString(i, test_msg);
        HAL_Delay(TEST_DELAY_MS);
        // 假设发送成功（实际项目中需要硬件回环测试）
        TEST_ASSERT(true);
        // UART发送功能
    }
    return true;
}
/**
 * @brief 测试数据包验证功能
 * @retval true: 通过, false: 失败
 */
static bool test_packet_validation(void)
{
    //printf(" 测试数据包验证功能 --");
    TestBoardPacket_t packet;
    uint8_t test_data[] = {0x01, 0x02, 0x03};
    // 测试正常数据包构建
    bool build_result = TestBoard_BuildPacket(&packet, TEST_BOARD_EL1, CMD_POWER_ON, test_data, 3);
    TEST_ASSERT(build_result == true);
    // 测试数据包验证
    bool validate_result = TestBoard_ValidatePacket(&packet);
    TEST_ASSERT(validate_result == true);
    // 测试起始字节验证
    TEST_ASSERT(packet.start_byte == PACKET_START_BYTE);
    // 测试结束字节验证
    TEST_ASSERT(packet.end_byte == PACKET_END_BYTE);
    // 测试板卡ID验证
    TEST_ASSERT(packet.board_id == TEST_BOARD_EL1);
    // 测试指令验证
    TEST_ASSERT(packet.command == CMD_POWER_ON);
    // 测试数据长度验证
    TEST_ASSERT(packet.data_length == 3);
    // 测试数据内容验证
    TEST_ASSERT(memcmp(packet.data, test_data, 3) == 0);
    // 测试无效数据包
    packet.start_byte = 0x00;
    // 错误的起始字节
    bool invalid_result = TestBoard_ValidatePacket(&packet);
    TEST_ASSERT(invalid_result == false);
    return true;
}
/**
 * @brief 测试测试板控制指令
 * @retval true: 通过, false: 失败
 */
static bool test_board_control_commands(void)
{
    //printf(" 测试测试板控制指令 ---");
    // 测试测试板信息获取
    TestBoardInfo_t *info = TestBoard_GetInfo(TEST_BOARD_EL1);
    TEST_ASSERT(info != NULL);
    TEST_ASSERT(info->type == TEST_BOARD_EL1);
    // 测试上电指令
    bool power_on_result = TestBoard_PowerOn(TEST_BOARD_EL1);
    TEST_ASSERT(power_on_result == true);
    HAL_Delay(TEST_DELAY_MS);
    // 测试状态获取
    TestBoardStatus_t status = TestBoard_GetStatus(TEST_BOARD_EL1);
    TEST_ASSERT(status == BOARD_STATUS_IDLE);
    // 测试电压设置
    bool voltage_result = TestBoard_SetVoltage(TEST_BOARD_EL1, 3300);
    TEST_ASSERT(voltage_result == true);
    HAL_Delay(TEST_DELAY_MS);
    // 测试电流设置
    bool current_result = TestBoard_SetCurrent(TEST_BOARD_EL1, 1000);
    TEST_ASSERT(current_result == true);
    HAL_Delay(TEST_DELAY_MS);
    // 测试开始测试指令
    bool start_test_result = TestBoard_StartTest(TEST_BOARD_EL1);
    TEST_ASSERT(start_test_result == true);
    HAL_Delay(TEST_DELAY_MS);
    // 验证状态变化
    status = TestBoard_GetStatus(TEST_BOARD_EL1);
    TEST_ASSERT(status == BOARD_STATUS_TESTING);
    // 测试停止测试指令
    bool stop_test_result = TestBoard_StopTest(TEST_BOARD_EL1);
    TEST_ASSERT(stop_test_result == true);
    HAL_Delay(TEST_DELAY_MS);
    // 测试复位指令
    bool reset_result = TestBoard_Reset(TEST_BOARD_EL1);
    TEST_ASSERT(reset_result == true);
    HAL_Delay(TEST_DELAY_MS);
    // 测试断电指令
    bool power_off_result = TestBoard_PowerOff(TEST_BOARD_EL1);
    TEST_ASSERT(power_off_result == true);
    // 测试批量操作
    bool power_on_all_result = TestBoard_PowerOnAll();
    TEST_ASSERT(power_on_all_result == true);
    HAL_Delay(TEST_DELAY_MS * 2);
    bool power_off_all_result = TestBoard_PowerOffAll();
    TEST_ASSERT(power_off_all_result == true);
    return true;
}
/**
 * @brief 测试显示功能
 * @retval true: 通过, false: 失败
 */
static bool test_display_functions(void)
{
    printf("--- 测试显示功能 ---");
    // 测试消息显示
    Display_ShowMessage("Test Message 1");
    HAL_Delay(TEST_DELAY_MS);
    Display_ShowMessage("Test Message 2");
    HAL_Delay(TEST_DELAY_MS);
    // 测试板卡状态显示
    for (uint8_t i = 0; i < BOARD_COUNT; i++)
    {
        char status_msg[16];
        snprintf(status_msg, sizeof(status_msg), "TEST_%d", i);
        Display_ShowBoardStatus((TestBoardType_t)i, status_msg);
    }
    HAL_Delay(TEST_DELAY_MS);
    // 测试状态更新
    TestBoard_UpdateStatus();
    Display_UpdateStatus();
    // 所有显示功能测试通过（无法自动验证显示内容
    TEST_ASSERT(true); // 显示消息功能
    TEST_ASSERT(true); // 板卡状态显示功能
    TEST_ASSERT(true); // 状态更新功能
    return true;
}
/**
 * @brief 测试错误处理功能
 * @retval true: 通过, false: 失败
 */
static bool test_error_handling(void)
{
    printf("--- 测试错误处理功能 ---");
    // 测试无效板卡ID
    bool invalid_board_result = TestBoard_PowerOn((TestBoardType_t)255);
    TEST_ASSERT(invalid_board_result == false);
    // 测试无效数据包构建
    TestBoardPacket_t packet;
    uint8_t large_data[PACKET_MAX_DATA_LEN + 10];
    bool invalid_packet_result = TestBoard_BuildPacket(&packet, TEST_BOARD_EL1, CMD_POWER_ON, large_data, sizeof(large_data));
    TEST_ASSERT(invalid_packet_result == false);
    // 测试错误处理函数
    //TestBoard_ErrorHandler(TEST_BOARD_EL1, 0x1001);
    TestBoardStatus_t error_status = TestBoard_GetStatus(TEST_BOARD_EL1);
    TEST_ASSERT(error_status == BOARD_STATUS_ERROR);
    // 测试紧急停止
    TestBoard_EmergencyStop();
    for (uint8_t i = 0; i < BOARD_COUNT; i++)
    {
        TestBoardStatus_t status = TestBoard_GetStatus((TestBoardType_t)i);
        TEST_ASSERT(status == BOARD_STATUS_ERROR);
    }
    return true;
}
/**
 * @brief 打印测试总结\
 */
static void print_test_summary(void)
{
    // printf("=== 测试总结 ===");
    // printf("总测试数: %lu", test_result.test_count);
    // printf("通过数: %lu", test_result.pass_count);
    // printf("失败数: %lu", test_result.fail_count);
    // if (test_result.fail_count == 0)
    // {
    //     printf("[SUCCESS] 所有测试通过!");
    // }
    // else
    // {
    //     printf("[WARNING] %lu个测试失败!", test_result.fail_count);
    // }
    // printf("成功率: %.1f%%", (float)test_result.pass_count / test_result.test_count * 100.0f);
    // printf("==================");
}
/**
 * @brief 获取测试结果
 * @retval 测试结果结构体指针
 */
TestResult_t *TestBoard_GetTestResult(void)
{
    return &test_result;
}
/**
 * @brief 重置测试结果
 */
void TestBoard_ResetTestResult(void)
{
    memset(&test_result, 0, sizeof(TestResult_t));
}
/* USER CODE END 0 */