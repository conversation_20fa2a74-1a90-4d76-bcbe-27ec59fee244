/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : test_board_control.c
  * @brief          : 测试板控制系统实现
  ******************************************************************************
  * @attention
  *
  * 测试板控制系统实现
  * 包含基础控制函数、参数设置函数、批量控制函数和高级功能
  * 每个测试板通过独立的串口进行控制，支持并发操作
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "test_board_control.h"
#include "string.h"
#include "stdio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN PV */

// 全局测试板信息数组
static TestBoardInfo_t board_info[BOARD_COUNT];

// 显示信息
static DisplayInfo_t display_info = {0};

// 外部串口函数声明（来自main.c）
extern void IOUART_SendByte(uint8_t uart_id, uint8_t ch);
extern void IOUART_PutChar(uint8_t uart_id, uint8_t ch);
extern void IOUART_SendString(uint8_t uart_id, const char *s);

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN PFP */

static bool send_packet_to_board(TestBoardType_t board_type, TestBoardPacket_t *packet);
static bool receive_packet_from_board(TestBoardType_t board_type, TestBoardPacket_t *packet, uint32_t timeout_ms);
static void update_board_info(TestBoardType_t board_type, TestBoardStatus_t status);

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/**
 * @brief 计算数组元素和
 * @param array: 数组指针
 * @param len: 数组长度
 * @retval 数组元素和
 */
uint8_t sum_array(uint8_t *array, uint8_t len) {
    uint32_t sum = 0;
    for (uint8_t i = 0; i < len; i++) {
        sum += array[i];
    }
    return (uint8_t)(sum & 0xFF);
}

/**
 * @brief 获取测试板类型名称
 * @param type: 测试板类型
 * @retval 类型名称字符串
 */
const char* TestBoard_GetTypeName(TestBoardType_t type) {
    switch (type) {
        case TEST_BOARD_EL1: return "EL1";
        case TEST_BOARD_EL2: return "EL2";
        case TEST_BOARD_EL3: return "EL3";
        case TEST_BOARD_EL4: return "EL4";
        case TEC_BOARD_1:    return "TEC1";
        case TEC_BOARD_2:    return "TEC2";
        case TEC_BOARD_3:    return "TEC3";
        case TEC_BOARD_4:    return "TEC4";
        default:             return "UNKNOWN";
    }
}

/**
 * @brief 获取测试板状态名称
 * @param status: 测试板状态
 * @retval 状态名称字符串
 */
const char* TestBoard_GetStatusName(TestBoardStatus_t status) {
    switch (status) {
        case BOARD_STATUS_OFFLINE:     return "OFFLINE";
        case BOARD_STATUS_IDLE:        return "IDLE";
        case BOARD_STATUS_TESTING:     return "TESTING";
        case BOARD_STATUS_ERROR:       return "ERROR";
        case BOARD_STATUS_CALIBRATING: return "CALIBRATING";
        default:                       return "UNKNOWN";
    }
}

/**
 * @brief 初始化测试板控制系统
 * @retval true: 成功, false: 失败
 */
bool TestBoard_Init(void) {
    // 初始化所有板卡信息
    for (uint8_t i = 0; i < BOARD_COUNT; i++) {
        board_info[i].type = (TestBoardType_t)i;
        board_info[i].status = BOARD_STATUS_OFFLINE;
        board_info[i].uart_id = i;  // 每个板卡对应一个串口
        board_info[i].last_heartbeat_time = 0;
        board_info[i].voltage_mv = 0;
        board_info[i].current_ma = 0;
        board_info[i].test_count = 0;
        board_info[i].error_count = 0;
        board_info[i].communication_ok = false;
    }
    
    // 初始化显示信息
    memset(&display_info, 0, sizeof(DisplayInfo_t));
    display_info.display_updated = true;
    
    return true;
}

/**
 * @brief 构建数据包
 * @param packet: 数据包指针
 * @param board_id: 板卡ID
 * @param cmd: 指令代码
 * @param data: 数据内容
 * @param len: 数据长度
 * @retval true: 成功, false: 失败
 */
bool TestBoard_BuildPacket(TestBoardPacket_t *packet, uint8_t board_id, uint8_t cmd, uint8_t *data, uint8_t len) {
    if (!packet || len > PACKET_MAX_DATA_LEN) {
        return false;
    }
    
    packet->start_byte = PACKET_START_BYTE;
    packet->board_id = board_id;
    packet->command = cmd;
    packet->data_length = len;
    
    // 复制数据
    if (data && len > 0) {
        memcpy(packet->data, data, len);
    }
    
    // 计算校验和
    packet->checksum = CALCULATE_CHECKSUM(packet);
    packet->end_byte = PACKET_END_BYTE;
    
    return true;
}

/**
 * @brief 验证数据包
 * @param packet: 数据包指针
 * @retval true: 有效, false: 无效
 */
bool TestBoard_ValidatePacket(TestBoardPacket_t *packet) {
    if (!packet) {
        return false;
    }
    
    // 检查起始和结束字节
    if (packet->start_byte != PACKET_START_BYTE || packet->end_byte != PACKET_END_BYTE) {
        return false;
    }
    
    // 检查板卡ID范围
    if (packet->board_id >= BOARD_COUNT) {
        return false;
    }
    
    // 检查数据长度
    if (packet->data_length > PACKET_MAX_DATA_LEN) {
        return false;
    }
    
    // 验证校验和
    uint8_t calculated_checksum = CALCULATE_CHECKSUM(packet);
    if (packet->checksum != calculated_checksum) {
        return false;
    }
    
    return true;
}

/**
 * @brief 发送数据包到指定测试板
 * @param board_type: 测试板类型
 * @param packet: 数据包指针
 * @retval true: 成功, false: 失败
 */
bool TestBoard_SendPacket(TestBoardType_t board_type, TestBoardPacket_t *packet) {
    if (board_type >= BOARD_COUNT || !packet) {
        return false;
    }
    
    uint8_t uart_id = board_info[board_type].uart_id;
    uint8_t *packet_bytes = (uint8_t*)packet;
    
    // 发送数据包，使用非阻塞方式
    for (uint16_t i = 0; i < sizeof(TestBoardPacket_t); i++) {
        IOUART_PutChar(uart_id, packet_bytes[i]);
    }
    
    return true;
}

/**
 * @brief 测试板上电
 * @param board_type: 测试板类型
 * @retval true: 成功, false: 失败
 */
bool TestBoard_PowerOn(TestBoardType_t board_type) {
    if (board_type >= BOARD_COUNT) {
        return false;
    }
    
    TestBoardPacket_t packet;
    if (!TestBoard_BuildPacket(&packet, board_type, CMD_POWER_ON, NULL, 0)) {
        return false;
    }
    
    bool result = TestBoard_SendPacket(board_type, &packet);
    if (result) {
        update_board_info(board_type, BOARD_STATUS_IDLE);
    }
    
    return result;
}

/**
 * @brief 测试板断电
 * @param board_type: 测试板类型
 * @retval true: 成功, false: 失败
 */
bool TestBoard_PowerOff(TestBoardType_t board_type) {
    if (board_type >= BOARD_COUNT) {
        return false;
    }
    
    TestBoardPacket_t packet;
    if (!TestBoard_BuildPacket(&packet, board_type, CMD_POWER_OFF, NULL, 0)) {
        return false;
    }
    
    bool result = TestBoard_SendPacket(board_type, &packet);
    if (result) {
        update_board_info(board_type, BOARD_STATUS_OFFLINE);
    }
    
    return result;
}

/**
 * @brief 测试板复位
 * @param board_type: 测试板类型
 * @retval true: 成功, false: 失败
 */
bool TestBoard_Reset(TestBoardType_t board_type) {
    if (board_type >= BOARD_COUNT) {
        return false;
    }
    
    TestBoardPacket_t packet;
    if (!TestBoard_BuildPacket(&packet, board_type, CMD_RESET, NULL, 0)) {
        return false;
    }
    
    bool result = TestBoard_SendPacket(board_type, &packet);
    if (result) {
        update_board_info(board_type, BOARD_STATUS_IDLE);
    }
    
    return result;
}

/**
 * @brief 获取测试板状态
 * @param board_type: 测试板类型
 * @retval 测试板状态
 */
TestBoardStatus_t TestBoard_GetStatus(TestBoardType_t board_type) {
    if (board_type >= BOARD_COUNT) {
        return BOARD_STATUS_OFFLINE;
    }
    
    return board_info[board_type].status;
}

/**
 * @brief 设置测试板电压
 * @param board_type: 测试板类型
 * @param voltage_mv: 电压值(毫伏)
 * @retval true: 成功, false: 失败
 */
bool TestBoard_SetVoltage(TestBoardType_t board_type, uint16_t voltage_mv) {
    if (board_type >= BOARD_COUNT) {
        return false;
    }
    
    uint8_t data[2];
    data[0] = (uint8_t)(voltage_mv & 0xFF);
    data[1] = (uint8_t)((voltage_mv >> 8) & 0xFF);
    
    TestBoardPacket_t packet;
    if (!TestBoard_BuildPacket(&packet, board_type, CMD_SET_VOLTAGE, data, 2)) {
        return false;
    }
    
    bool result = TestBoard_SendPacket(board_type, &packet);
    if (result) {
        board_info[board_type].voltage_mv = voltage_mv;
    }
    
    return result;
}

/**
 * @brief 设置测试板电流
 * @param board_type: 测试板类型
 * @param current_ma: 电流值(毫安)
 * @retval true: 成功, false: 失败
 */
bool TestBoard_SetCurrent(TestBoardType_t board_type, uint16_t current_ma) {
    if (board_type >= BOARD_COUNT) {
        return false;
    }
    
    uint8_t data[2];
    data[0] = (uint8_t)(current_ma & 0xFF);
    data[1] = (uint8_t)((current_ma >> 8) & 0xFF);
    
    TestBoardPacket_t packet;
    if (!TestBoard_BuildPacket(&packet, board_type, CMD_SET_CURRENT, data, 2)) {
        return false;
    }
    
    bool result = TestBoard_SendPacket(board_type, &packet);
    if (result) {
        board_info[board_type].current_ma = current_ma;
    }
    
    return result;
}

/**
 * @brief 开始测试
 * @param board_type: 测试板类型
 * @retval true: 成功, false: 失败
 */
bool TestBoard_StartTest(TestBoardType_t board_type) {
    if (board_type >= BOARD_COUNT) {
        return false;
    }
    
    TestBoardPacket_t packet;
    if (!TestBoard_BuildPacket(&packet, board_type, CMD_START_TEST, NULL, 0)) {
        return false;
    }
    
    bool result = TestBoard_SendPacket(board_type, &packet);
    if (result) {
        update_board_info(board_type, BOARD_STATUS_TESTING);
        board_info[board_type].test_count++;
    }
    
    return result;
}

/**
 * @brief 停止测试
 * @param board_type: 测试板类型
 * @retval true: 成功, false: 失败
 */
bool TestBoard_StopTest(TestBoardType_t board_type) {
    if (board_type >= BOARD_COUNT) {
        return false;
    }
    
    TestBoardPacket_t packet;
    if (!TestBoard_BuildPacket(&packet, board_type, CMD_STOP_TEST, NULL, 0)) {
        return false;
    }
    
    bool result = TestBoard_SendPacket(board_type, &packet);
    if (result) {
        update_board_info(board_type, BOARD_STATUS_IDLE);
    }
    
    return result;
}

/**
 * @brief 批量上电所有测试板
 * @retval true: 成功, false: 失败
 */
bool TestBoard_PowerOnAll(void) {
    bool all_success = true;
    
    for (uint8_t i = 0; i < BOARD_COUNT; i++) {
        if (!TestBoard_PowerOn((TestBoardType_t)i)) {
            all_success = false;
        }
        HAL_Delay(10); // 短暂延时避免冲突
    }
    
    return all_success;
}

/**
 * @brief 批量断电所有测试板
 * @retval true: 成功, false: 失败
 */
bool TestBoard_PowerOffAll(void) {
    bool all_success = true;
    
    for (uint8_t i = 0; i < BOARD_COUNT; i++) {
        if (!TestBoard_PowerOff((TestBoardType_t)i)) {
            all_success = false;
        }
        HAL_Delay(10); // 短暂延时避免冲突
    }
    
    return all_success;
}

/**
 * @brief 紧急停止所有测试板
 */
void TestBoard_EmergencyStop(void) {
    // 立即停止所有测试
    for (uint8_t i = 0; i < BOARD_COUNT; i++) {
        TestBoard_StopTest((TestBoardType_t)i);
        update_board_info((TestBoardType_t)i, BOARD_STATUS_ERROR);
    }
}

/**
 * @brief 获取测试板信息
 * @param board_type: 测试板类型
 * @retval 测试板信息指针
 */
TestBoardInfo_t* TestBoard_GetInfo(TestBoardType_t board_type) {
    if (board_type >= BOARD_COUNT) {
        return NULL;
    }
    
    return &board_info[board_type];
}

/**
 * @brief 更新板卡信息
 * @param board_type: 测试板类型
 * @param status: 新状态
 */
static void update_board_info(TestBoardType_t board_type, TestBoardStatus_t status) {
    if (board_type >= BOARD_COUNT) {
        return;
    }
    
    board_info[board_type].status = status;
    board_info[board_type].last_heartbeat_time = HAL_GetTick();
    board_info[board_type].communication_ok = true;
    
    // 更新显示信息
    strcpy(display_info.board_status[board_type], TestBoard_GetStatusName(status));
    display_info.display_updated = true;
}

/**
 * @brief 初始化大彩串口屏 (DC48270M043_1111_0C)
 * @retval true: 成功, false: 失败
 */
bool Display_Init(void) {
    // 初始化显示信息
    memset(&display_info, 0, sizeof(DisplayInfo_t));
    display_info.display_updated = true;
    
    // 等待串口屏启动完成
    HAL_Delay(2000);
    
    // 发送初始化命令到大彩串口屏
    Display_SendCommand("page 0");  // 设置默认页面
    HAL_Delay(100);
    
    // 设置背光亮度 (0-100)
    Display_SendCommand("dim=80");
    HAL_Delay(100);
    
    // 显示初始化消息
    Display_ShowMessage("System Init...");
    
    return true;
}

/**
 * @brief 发送命令到大彩串口屏
 * @param cmd: 命令字符串
 */
void Display_SendCommand(const char *cmd) {
    if (!cmd) return;
    
    // 发送命令
    IOUART_SendString(DISPLAY_UART_ID, cmd);
    
    // 发送结束符 (3个0xFF)
    IOUART_SendByte(DISPLAY_UART_ID, 0xFF);
    IOUART_SendByte(DISPLAY_UART_ID, 0xFF);
    IOUART_SendByte(DISPLAY_UART_ID, 0xFF);
}

/**
 * @brief 设置屏幕页面
 * @param page: 页面编号
 */
void Display_SetPage(uint8_t page) {
    char cmd[16];
    snprintf(cmd, sizeof(cmd), "page %d", page);
    Display_SendCommand(cmd);
}

/**
 * @brief 设置组件属性
 * @param component: 组件名称
 * @param attribute: 属性名称
 * @param value: 属性值
 */
void Display_SetComponent(const char *component, const char *attribute, const char *value) {
    if (!component || !attribute || !value) return;
    
    char cmd[128];
    snprintf(cmd, sizeof(cmd), "%s.%s=\"%s\"", component, attribute, value);
    Display_SendCommand(cmd);
}

/**
 * @brief 更新大彩串口屏显示
 */
void Display_UpdateStatus(void) {
    if (!display_info.display_updated) {
        return;
    }
    
    char value_buffer[64];
    
    // 显示系统时间 (假设有t0文本组件)
    snprintf(value_buffer, sizeof(value_buffer), "System Time: %u s", display_info.system_time);
    Display_SetComponent("t0", "txt", value_buffer);
    
    // 显示各板卡状态 (假设有t1-t8文本组件)
    for (uint8_t i = 0; i < BOARD_COUNT; i++) {
        char component[8];
        snprintf(component, sizeof(component), "t%d", i + 1);
        
        snprintf(value_buffer, sizeof(value_buffer), "%s: %s", 
                 TestBoard_GetTypeName((TestBoardType_t)i), display_info.board_status[i]);
        Display_SetComponent(component, "txt", value_buffer);
    }
    
    // 显示统计信息 (假设有t9文本组件)
    snprintf(value_buffer, sizeof(value_buffer), "Tests: %d, Errors: %d", 
             display_info.total_tests, display_info.total_errors);
    Display_SetComponent("t9", "txt", value_buffer);
    
    display_info.display_updated = false;
}

/**
 * @brief 在大彩串口屏上显示消息
 * @param message: 要显示的消息
 */
void Display_ShowMessage(const char *message) {
    if (!message) return;
    
    // 在消息显示区显示 (假设有t10文本组件)
    Display_SetComponent("t10", "txt", message);
}

/**
 * @brief 显示特定板卡状态
 * @param board_type: 测试板类型
 * @param status: 状态字符串
 */
void Display_ShowBoardStatus(TestBoardType_t board_type, const char *status) {
    if (board_type >= BOARD_COUNT || !status) return;
    
    strcpy(display_info.board_status[board_type], status);
    display_info.display_updated = true;
}

/**
 * @brief 更新系统状态
 */
void TestBoard_UpdateStatus(void) {
    uint32_t current_time = HAL_GetTick();
    
    // 更新系统时间
    display_info.system_time = current_time / 1000; // 转换为秒
    
    // 检查各板卡通信状态
    for (uint8_t i = 0; i < BOARD_COUNT; i++) {
        TestBoardInfo_t *info = &board_info[i];
        
        // 检查通信超时
        if (current_time - info->last_heartbeat_time > BOARD_COMM_TIMEOUT) {
            if (info->communication_ok) {
                info->communication_ok = false;
                info->status = BOARD_STATUS_OFFLINE;
                strcpy(display_info.board_status[i], "TIMEOUT");
                display_info.display_updated = true;
            }
        }
    }
    // 计算统计信息
    display_info.total_tests = 0;
    display_info.total_errors = 0;
    for (uint8_t i = 0; i < BOARD_COUNT; i++) {
        display_info.total_tests += board_info[i].test_count;
        display_info.total_errors += board_info[i].error_count;
    }
    display_info.display_updated = true;
}

/**
 * @brief 处理心跳数据包
 */
void TestBoard_ProcessHeartbeat(void) {
    static uint32_t last_heartbeat_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 检查是否需要发送心跳
    if (current_time - last_heartbeat_time >= HEARTBEAT_INTERVAL) {
        for (uint8_t i = 0; i < BOARD_COUNT; i++) {
            if (board_info[i].status != BOARD_STATUS_OFFLINE) {
                TestBoardPacket_t packet;
                if (TestBoard_BuildPacket(&packet, i, CMD_HEARTBEAT, NULL, 0)) {
                    TestBoard_SendPacket((TestBoardType_t)i, &packet);
                }
            }
        }
        last_heartbeat_time = current_time;
    }
}

/* USER CODE END 0 */