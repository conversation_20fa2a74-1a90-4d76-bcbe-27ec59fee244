/**
  ******************************************************************************
  * @file    stm32f2xx_hal_dcmi_ex.c
  * <AUTHOR> Application Team
  * @brief   Empty file; This file is no longer used to handle the Black&White
  *          feature. Its content is now moved to common files
  *          (stm32f2xx_hal_dcmi.c/.h) as there's no device's dependency within
  *          this family. It's just kept for compatibility reasons.
  *
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2016 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "stm32f2xx_hal.h"
/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Exported functions --------------------------------------------------------*/


